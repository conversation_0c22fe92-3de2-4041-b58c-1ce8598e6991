/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: #ecf0f1;
    min-height: 100vh;
}

/* Login Page Styles */
.login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.login-container {
    background: rgba(52, 73, 94, 0.9);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.game-logo h1 {
    font-size: 2.5rem;
    color: #f39c12;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.subtitle {
    color: #bdc3c7;
    font-style: italic;
    margin-bottom: 2rem;
}

.login-form h2 {
    margin-bottom: 1.5rem;
    color: #ecf0f1;
}

.login-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.google-btn {
    background: #db4437;
    color: white;
}

.google-btn:hover {
    background: #c23321;
}

.apple-btn {
    background: #000;
    color: white;
}

.apple-btn:hover {
    background: #333;
}

.login-status {
    margin-top: 1rem;
    padding: 0.5rem;
    border-radius: 5px;
    min-height: 1rem;
}

.login-status.error {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.login-status.success {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

/* Game Page Styles */
.game-page {
    background: linear-gradient(135deg, #1a252f, #2c3e50);
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(52, 73, 94, 0.8);
    border-bottom: 2px solid #f39c12;
}

.game-title h1 {
    font-size: 2rem;
    color: #f39c12;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-btn {
    padding: 0.5rem 1rem;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.logout-btn:hover {
    background: #c0392b;
}

/* Main Game Area */
.game-main {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* GDP Section */
.gdp-section {
    text-align: center;
    background: rgba(52, 73, 94, 0.6);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.gdp-display h2 {
    font-size: 2.5rem;
    color: #2ecc71;
    margin-bottom: 0.5rem;
}

.gdp-display p {
    color: #bdc3c7;
    margin-bottom: 1.5rem;
}

.gdp-click-btn {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
}

.gdp-click-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
}

.gdp-click-btn:active {
    transform: translateY(0);
}

/* Tabbed Interface */
.game-tabs {
    background: rgba(52, 73, 94, 0.6);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.tab-buttons {
    display: flex;
    background: rgba(44, 62, 80, 0.8);
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    background: transparent;
    color: #bdc3c7;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: rgba(52, 73, 94, 0.5);
    color: #ecf0f1;
}

.tab-btn.active {
    color: #f39c12;
    border-bottom-color: #f39c12;
    background: rgba(52, 73, 94, 0.3);
}

.tab-content {
    min-height: 400px;
}

.tab-panel {
    display: none;
    padding: 2rem;
}

.tab-panel.active {
    display: block;
}

.tab-panel h3 {
    color: #f39c12;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

/* Resource and Generator Lists */
.resource-list,
.generator-list {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.resource-item,
.generator-item {
    background: rgba(44, 62, 80, 0.6);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.generator-item {
    border-left-color: #2ecc71;
}

/* Footer */
.game-footer {
    background: rgba(52, 73, 94, 0.8);
    padding: 1rem 2rem;
    border-top: 2px solid #f39c12;
}

.game-stats {
    display: flex;
    justify-content: space-around;
    color: #bdc3c7;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: #34495e;
    padding: 2rem;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    text-align: center;
}

.event-choices {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.event-choice-btn {
    padding: 0.75rem 1.5rem;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.event-choice-btn:hover {
    background: #2980b9;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-content {
    text-align: center;
}

.loading-content h2 {
    color: #f39c12;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(243, 156, 18, 0.3);
    border-top: 5px solid #f39c12;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.disabled {
    opacity: 0.5;
    cursor: not-allowed !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .game-main {
        padding: 1rem;
    }
    
    .tab-buttons {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        min-width: 50%;
    }
    
    .game-stats {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .resource-list,
    .generator-list {
        grid-template-columns: 1fr;
    }
}

/* Loading Indicator */
.loading-indicator {
    text-align: center;
    padding: 2rem;
    color: #bdc3c7;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(243, 156, 18, 0.3);
    border-top: 4px solid #f39c12;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.auth-error {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border-radius: 5px;
    border-left: 4px solid #e74c3c;
}

.error-message {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border-radius: 5px;
    text-align: center;
}

/* Bedrock Widget Styling Overrides */
#bedrock-login-widget {
    margin: 1rem 0;
}

#bedrock-login-widget .container {
    background: rgba(44, 62, 80, 0.8) !important;
    border: 1px solid rgba(243, 156, 18, 0.3) !important;
}

#bedrock-login-widget button {
    transition: all 0.3s ease !important;
}

#bedrock-login-widget button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Fallback Authentication UI */
.fallback-auth {
    text-align: center;
    padding: 2rem;
    background: rgba(44, 62, 80, 0.8);
    border-radius: 10px;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.fallback-auth h3 {
    color: #f39c12;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.fallback-auth p {
    color: #bdc3c7;
    margin-bottom: 1.5rem;
}

.fallback-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.fallback-note {
    font-size: 0.9rem;
    color: #95a5a6;
    font-style: italic;
}

/* Animation Classes */
.click-animation {
    animation: clickPulse 0.3s ease;
}

@keyframes clickPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.purchase-animation {
    animation: purchaseGlow 0.5s ease;
}

@keyframes purchaseGlow {
    0% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
    50% { box-shadow: 0 0 20px rgba(46, 204, 113, 0.8); }
    100% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
}
/* U
I Manager Additions */
.generator-details, .resource-details {
    margin-bottom: 1rem;
}

.generator-count, .generator-production, .resource-amount, .resource-rate, .resource-total {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.label {
    color: #bdc3c7;
}

.value {
    color: #ecf0f1;
    font-weight: bold;
}

.generator-purchase, .generator-upgrade {
    background: rgba(44, 62, 80, 0.5);
    padding: 0.75rem;
    border-radius: 5px;
    margin-bottom: 0.75rem;
}

.purchase-info, .upgrade-info, .upgrade-cost {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.purchase-buttons {
    display: flex;
    gap: 0.5rem;
}

.purchase-btn, .upgrade-btn {
    padding: 0.5rem;
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
}

.purchase-btn:hover, .upgrade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.purchase-btn:active, .upgrade-btn:active {
    transform: translateY(0);
}

.upgrade-btn {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    width: 100%;
    margin-top: 0.5rem;
}

.game-notification {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Floating number animation */
@keyframes float-up {
    0% { opacity: 1; transform: translate(-50%, -50%); }
    100% { opacity: 0; transform: translate(-50%, -150%); }
}

.floating-number {
    position: absolute;
    pointer-events: none;
    z-index: 1000;
    animation: float-up 1s ease-out forwards;
}