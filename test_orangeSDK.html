<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orange SDK Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        #log { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: scroll; }
    </style>
</head>
<body>
    <h1>Orange SDK Integration Test</h1>
    
    <div class="test-section">
        <h2>SDK Status</h2>
        <p>SDK Available: <span id="sdk-status">Checking...</span></p>
    </div>
    
    <div class="test-section">
        <h2>Test Actions</h2>
        <button onclick="testInitialization()">Test Initialization</button>
        <button onclick="testSaveLoad()">Test Save/Load</button>
        <button onclick="testEventListeners()">Test Event Listeners</button>
        <button onclick="testStandaloneMode()">Test Standalone Mode</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log"></div>
    </div>

    <!-- Include required scripts -->
    <script src="js/utils.js"></script>
    <script src="js/orangeSDK.js"></script>
    <script src="js/gameEngine.js"></script>

    <script>
        let orangeSDK;
        let gameEngine;
        
        // Mock Orange SDK for testing
        window.GGSDK = {
            gameLoaded: () => log('MOCK: gameLoaded called', 'info'),
            gameOver: (score) => log(`MOCK: gameOver called with score: ${score}`, 'info'),
            saveGameData: (data) => log('MOCK: saveGameData called', 'info'),
            getGameData: (defaultData, callback) => {
                log('MOCK: getGameData called', 'info');
                setTimeout(() => callback(defaultData), 100);
            },
            listenPaused: (callback) => {
                log('MOCK: listenPaused registered', 'info');
                window.mockPauseCallback = callback;
            },
            listenResumed: (callback) => {
                log('MOCK: listenResumed registered', 'info');
                window.mockResumeCallback = callback;
            },
            listenQuit: (callback) => {
                log('MOCK: listenQuit registered', 'info');
                window.mockQuitCallback = callback;
            }
        };
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function testInitialization() {
            log('=== Testing Initialization ===', 'info');
            
            try {
                // Create Orange SDK manager
                orangeSDK = new OrangeSDKManager();
                log('✓ OrangeSDKManager created', 'success');
                
                // Create game engine
                gameEngine = new GameEngine(orangeSDK);
                log('✓ GameEngine created', 'success');
                
                // Initialize SDK
                await orangeSDK.initialize(gameEngine);
                log('✓ Orange SDK initialized', 'success');
                
                // Check SDK status
                const isReady = orangeSDK.isSDKReady();
                log(`SDK Ready: ${isReady}`, isReady ? 'success' : 'error');
                
                // Signal game loaded
                orangeSDK.gameLoaded();
                log('✓ Game loaded signal sent', 'success');
                
            } catch (error) {
                log(`✗ Initialization failed: ${error.message}`, 'error');
            }
        }
        
        async function testSaveLoad() {
            log('=== Testing Save/Load ===', 'info');
            
            if (!orangeSDK || !gameEngine) {
                log('✗ SDK or GameEngine not initialized', 'error');
                return;
            }
            
            try {
                // Create test game state
                const testState = {
                    gdp: 1000,
                    resources: { food: 50, industry: 25 },
                    generators: { farm: { count: 5 } },
                    version: "1.0.0"
                };
                
                // Save game data
                orangeSDK.saveGameData(testState);
                log('✓ Game data saved', 'success');
                
                // Load game data
                const defaultData = gameEngine.getDefaultGameState();
                orangeSDK.getGameData(defaultData, (loadedData) => {
                    log('✓ Game data loaded', 'success');
                    log(`Loaded GDP: ${loadedData.gdp}`, 'info');
                    
                    // Validate data
                    if (loadedData.gdp === testState.gdp) {
                        log('✓ Data validation passed', 'success');
                    } else {
                        log('✗ Data validation failed', 'error');
                    }
                });
                
            } catch (error) {
                log(`✗ Save/Load failed: ${error.message}`, 'error');
            }
        }
        
        function testEventListeners() {
            log('=== Testing Event Listeners ===', 'info');
            
            if (!orangeSDK) {
                log('✗ SDK not initialized', 'error');
                return;
            }
            
            try {
                // Add event listeners
                orangeSDK.onPaused(() => log('Event: Game paused', 'info'));
                orangeSDK.onResumed(() => log('Event: Game resumed', 'info'));
                orangeSDK.onQuit(() => log('Event: Game quit requested', 'info'));
                
                log('✓ Event listeners registered', 'success');
                
                // Test mock events
                setTimeout(() => {
                    log('Triggering mock pause event...', 'info');
                    if (window.mockPauseCallback) window.mockPauseCallback();
                }, 1000);
                
                setTimeout(() => {
                    log('Triggering mock resume event...', 'info');
                    if (window.mockResumeCallback) window.mockResumeCallback();
                }, 2000);
                
                setTimeout(() => {
                    log('Triggering mock quit event...', 'info');
                    if (window.mockQuitCallback) window.mockQuitCallback();
                }, 3000);
                
            } catch (error) {
                log(`✗ Event listener test failed: ${error.message}`, 'error');
            }
        }
        
        function testStandaloneMode() {
            log('=== Testing Standalone Mode ===', 'info');
            
            try {
                // Temporarily disable SDK to test standalone mode
                const originalSDK = window.GGSDK;
                delete window.GGSDK;
                
                // Create new SDK manager without SDK available
                const standaloneSDK = new OrangeSDKManager();
                log('✓ OrangeSDKManager created in standalone mode', 'success');
                
                // Test save in standalone mode
                standaloneSDK.saveGameData({ gdp: 500, test: true });
                log('✓ Save handled gracefully in standalone mode', 'success');
                
                // Test load in standalone mode
                standaloneSDK.getGameData({ gdp: 0 }, (data) => {
                    log('✓ Load handled gracefully in standalone mode', 'success');
                    log(`Returned default GDP: ${data.gdp}`, 'info');
                });
                
                // Test game loaded signal
                standaloneSDK.gameLoaded();
                log('✓ Game loaded signal handled in standalone mode', 'success');
                
                // Test game over signal
                standaloneSDK.gameOver(1000);
                log('✓ Game over signal handled in standalone mode', 'success');
                
                // Restore original SDK
                if (originalSDK) {
                    window.GGSDK = originalSDK;
                }
                
            } catch (error) {
                log(`✗ Standalone mode test failed: ${error.message}`, 'error');
            }
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Check SDK status
            const sdkStatus = document.getElementById('sdk-status');
            
            sdkStatus.textContent = typeof GGSDK !== 'undefined' ? 'Available (Mock)' : 'Not Available';
            sdkStatus.className = typeof GGSDK !== 'undefined' ? 'success' : 'error';
            
            log('Test page loaded and ready', 'info');
        });
    </script>
</body>
</html>