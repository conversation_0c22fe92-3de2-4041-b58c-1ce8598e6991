<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Resources Test - Pooristan</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-summary {
            font-weight: bold;
            font-size: 1.2em;
            margin-top: 20px;
        }
        .resource-display {
            background: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Multiple Resources System Test</h1>
    
    <div class="test-container">
        <h2>Test Results</h2>
        <div id="test-results"></div>
        <div id="test-summary" class="test-summary"></div>
    </div>
    
    <div class="test-container">
        <h2>Game State</h2>
        <div id="game-state"></div>
    </div>

    <!-- Include game modules -->
    <script src="js/utils.js"></script>
    <script src="js/gameEngine.js"></script>
    
    <script>
        // Test suite for multiple resources system
        class MultipleResourcesTest {
            constructor() {
                this.results = [];
                this.engine = new GameEngine();
            }
            
            runAllTests() {
                console.log('Starting Multiple Resources System Tests...');
                
                // Test 1: Default state has all resource types
                this.testDefaultResourceTypes();
                
                // Test 2: All generators are configured correctly
                this.testGeneratorConfiguration();
                
                // Test 3: Resource-based costs work
                this.testResourceBasedCosts();
                
                // Test 4: Generator unlocking works
                this.testGeneratorUnlocking();
                
                // Test 5: Resource generation works for all types
                this.testResourceGeneration();
                
                // Test 6: Resource validation works
                this.testResourceValidation();
                
                // Test 7: Multiple resource purchases work
                this.testMultipleResourcePurchases();
                
                // Display results
                this.displayResults();
                this.displayGameState();
            }
            
            testDefaultResourceTypes() {
                const gameState = this.engine.getGameState();
                const expectedResources = ['food', 'industry', 'knowledge', 'influence', 'bitcoin'];
                
                let allResourcesExist = true;
                for (const resource of expectedResources) {
                    if (!gameState.resources.hasOwnProperty(resource)) {
                        allResourcesExist = false;
                        break;
                    }
                }
                
                this.addResult('Default state contains all resource types', allResourcesExist);
                
                // Check resource rates exist
                let allRatesExist = true;
                for (const resource of expectedResources) {
                    if (!gameState.resourcesPerSecond.hasOwnProperty(resource)) {
                        allRatesExist = false;
                        break;
                    }
                }
                
                this.addResult('Default state contains all resource generation rates', allRatesExist);
            }
            
            testGeneratorConfiguration() {
                const gameState = this.engine.getGameState();
                const expectedGenerators = ['farm', 'factory', 'school', 'embassy', 'bitcoinMiner'];
                
                let allGeneratorsExist = true;
                for (const generator of expectedGenerators) {
                    if (!gameState.generators.hasOwnProperty(generator)) {
                        allGeneratorsExist = false;
                        break;
                    }
                }
                
                this.addResult('All generator types exist', allGeneratorsExist);
                
                // Check that generators have resource-based costs
                const factory = gameState.generators.factory;
                const hasResourceCost = typeof factory.baseCost === 'object' && factory.baseCost.gdp;
                this.addResult('Generators have resource-based costs', hasResourceCost);
                
                // Check unlock requirements
                const farmUnlocked = gameState.generators.farm.unlocked === true;
                const factoryLocked = gameState.generators.factory.unlocked === false;
                this.addResult('Farm is unlocked by default', farmUnlocked);
                this.addResult('Factory is locked by default', factoryLocked);
            }
            
            testResourceBasedCosts() {
                // Test farm purchase (GDP cost)
                this.engine.gameState.gdp = 100;
                const farmCost = this.engine.calculateGeneratorCost('farm');
                const canAffordFarm = this.engine.canAffordResourceCost(farmCost);
                this.addResult('Can afford farm with sufficient GDP', canAffordFarm);
                
                // Test school purchase (food + industry cost)
                this.engine.gameState.resources.food = 200;
                this.engine.gameState.resources.industry = 100;
                this.engine.unlockGenerator('school');
                const schoolCost = this.engine.calculateGeneratorCost('school');
                const canAffordSchool = this.engine.canAffordResourceCost(schoolCost);
                this.addResult('Can afford school with sufficient food and industry', canAffordSchool);
            }
            
            testGeneratorUnlocking() {
                // Reset state
                this.engine.gameState.gdp = 100;
                
                // Test factory unlock
                const factoryUnlockedBefore = this.engine.gameState.generators.factory.unlocked;
                this.engine.checkGeneratorUnlocks();
                const factoryUnlockedAfter = this.engine.gameState.generators.factory.unlocked;
                
                this.addResult('Factory unlocks when GDP >= 50', !factoryUnlockedBefore && factoryUnlockedAfter);
            }
            
            testResourceGeneration() {
                // Purchase a farm and test food generation
                this.engine.gameState.gdp = 100;
                const farmPurchased = this.engine.purchaseGenerator('farm', 1);
                this.addResult('Can purchase farm generator', farmPurchased);
                
                if (farmPurchased) {
                    this.engine.updateResourceRates();
                    const foodRate = this.engine.gameState.resourcesPerSecond.food;
                    this.addResult('Farm generates food per second', foodRate > 0);
                }
            }
            
            testResourceValidation() {
                // Test negative resource correction
                this.engine.gameState.resources.food = -10;
                this.engine.validateGameState();
                const foodCorrected = this.engine.gameState.resources.food === 0;
                this.addResult('Negative resources are corrected to 0', foodCorrected);
            }
            
            testMultipleResourcePurchases() {
                // Set up resources for school purchase
                this.engine.gameState.resources.food = 200;
                this.engine.gameState.resources.industry = 100;
                this.engine.unlockGenerator('school');
                
                const initialFood = this.engine.gameState.resources.food;
                const initialIndustry = this.engine.gameState.resources.industry;
                
                const schoolPurchased = this.engine.purchaseGenerator('school', 1);
                this.addResult('Can purchase school with multiple resource costs', schoolPurchased);
                
                if (schoolPurchased) {
                    const foodDeducted = this.engine.gameState.resources.food < initialFood;
                    const industryDeducted = this.engine.gameState.resources.industry < initialIndustry;
                    this.addResult('Multiple resources are deducted correctly', foodDeducted && industryDeducted);
                }
            }
            
            addResult(testName, passed) {
                this.results.push({ test: testName, passed: passed });
                console.log(`${passed ? '✓' : '✗'} ${testName}`);
            }
            
            displayResults() {
                const resultsDiv = document.getElementById('test-results');
                const summaryDiv = document.getElementById('test-summary');
                
                let html = '';
                let passCount = 0;
                
                this.results.forEach(result => {
                    const className = result.passed ? 'test-pass' : 'test-fail';
                    const icon = result.passed ? '✓' : '✗';
                    html += `<div class="test-result ${className}">${icon} ${result.test}</div>`;
                    if (result.passed) passCount++;
                });
                
                resultsDiv.innerHTML = html;
                
                const totalTests = this.results.length;
                const passRate = ((passCount / totalTests) * 100).toFixed(1);
                summaryDiv.innerHTML = `Tests Passed: ${passCount}/${totalTests} (${passRate}%)`;
                summaryDiv.className = `test-summary ${passCount === totalTests ? 'test-pass' : 'test-fail'}`;
            }
            
            displayGameState() {
                const stateDiv = document.getElementById('game-state');
                const gameState = this.engine.getGameState();
                
                let html = '<div class="resource-display">';
                html += `<h3>Resources</h3>`;
                html += `<p>GDP: ${gameState.gdp}</p>`;
                for (const resource in gameState.resources) {
                    html += `<p>${resource}: ${gameState.resources[resource]}</p>`;
                }
                html += '</div>';
                
                html += '<div class="resource-display">';
                html += `<h3>Generators</h3>`;
                for (const generator in gameState.generators) {
                    const gen = gameState.generators[generator];
                    html += `<p>${generator}: ${gen.count} owned, ${gen.unlocked ? 'unlocked' : 'locked'}</p>`;
                }
                html += '</div>';
                
                stateDiv.innerHTML = html;
            }
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            const tester = new MultipleResourcesTest();
            tester.runAllTests();
        });
    </script>
</body>
</html>
