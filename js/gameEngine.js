/**
 * GameEngine - Core game logic and state management for Pooristan
 * Handles GDP clicking, resource generation, and game loop management
 */
class GameEngine {
    constructor(orangeSDK = null) {
        this.gameState = this.getDefaultGameState();
        this.gameLoop = null;
        this.lastUpdateTime = 0;
        this.fixedTimeStep = 1000 / 60; // 60 FPS
        this.accumulator = 0;
        this.isRunning = false;
        this.lastSaveTime = 0;
        this.saveInterval = 30000; // Save every 30 seconds
        this.orangeSDK = orangeSDK;
        this.uiManager = null;
        
        // Bind methods to preserve context
        this.tick = this.tick.bind(this);
        this.loop = this.loop.bind(this);
    }
    
    /**
     * Set UI Manager reference
     */
    setUIManager(uiManager) {
        this.uiManager = uiManager;
    }

    /**
     * Get default game state structure
     */
    getDefaultGameState() {
        return {
            // Core resources
            gdp: 0,
            gdpPerSecond: 0,
            totalGDPEarned: 0,
            
            // Secondary resources
            resources: {
                food: 0,
                industry: 0,
                knowledge: 0,
                influence: 0,
                bitcoin: 0
            },
            
            // Resource generation rates
            resourcesPerSecond: {
                food: 0,
                industry: 0,
                knowledge: 0,
                influence: 0,
                bitcoin: 0
            },
            
            // Generators for all resource types
            generators: {
                farm: {
                    count: 0,
                    baseProduction: 1,
                    baseCost: { gdp: 10 },
                    costMultiplier: 1.15,
                    resourceType: 'food',
                    unlockRequirement: null,
                    unlocked: true,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                },
                factory: {
                    count: 0,
                    baseProduction: 2,
                    baseCost: { gdp: 100 },
                    costMultiplier: 1.2,
                    resourceType: 'industry',
                    unlockRequirement: 'industrialization',
                    unlocked: false,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                },
                school: {
                    count: 0,
                    baseProduction: 0.5,
                    baseCost: { food: 100, industry: 50 },
                    costMultiplier: 1.25,
                    resourceType: 'knowledge',
                    unlockRequirement: 'education',
                    unlocked: false,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                },
                embassy: {
                    count: 0,
                    baseProduction: 0.1,
                    baseCost: { knowledge: 100, industry: 100 },
                    costMultiplier: 1.3,
                    resourceType: 'influence',
                    unlockRequirement: 'diplomacy',
                    unlocked: false,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                },
                bitcoinMiner: {
                    count: 0,
                    baseProduction: 0.01,
                    baseCost: { knowledge: 500, industry: 1000 },
                    costMultiplier: 1.4,
                    resourceType: 'bitcoin',
                    unlockRequirement: 'cryptocurrency',
                    unlocked: false,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                }
            },
            
            // Game metadata
            gameStartTime: Date.now(),
            totalPlayTime: 0,
            totalClicks: 0,
            
            // Version for save compatibility
            version: "1.0.0"
        };
    }

    /**
     * Handle GDP click - core clicking mechanism
     * Requirements: 1.1, 1.2
     */
    clickGDP() {
        this.gameState.gdp += 1;
        this.gameState.totalGDPEarned += 1;
        this.gameState.totalClicks += 1;
        
        // Validate state after click
        this.validateGameState();
        
        return this.gameState.gdp;
    }

    /**
     * Purchase generator with validation
     * Requirements: 2.1, 2.5, 7.4
     */
    purchaseGenerator(type, quantity = 1) {
        if (!this.gameState.generators[type]) {
            console.error(`Generator type ${type} not found`);
            return false;
        }

        const generator = this.gameState.generators[type];

        // Check if generator is unlocked
        if (!generator.unlocked) {
            console.warn(`Generator ${type} is not unlocked yet`);
            return false;
        }

        const cost = this.calculateGeneratorCost(type, quantity);

        // Check if player can afford the purchase
        if (!this.canAffordResourceCost(cost)) {
            return false;
        }

        // Deduct cost and add generators
        if (!this.deductResourceCost(cost)) {
            return false;
        }

        generator.count += quantity;

        // Recalculate resource rates
        this.updateResourceRates();

        // Validate state after purchase
        this.validateGameState();

        return true;
    }

    /**
     * Upgrade generator with validation
     * Requirements: 2.3, 2.4, 7.4
     */
    upgradeGenerator(type) {
        if (!this.gameState.generators[type]) {
            console.error(`Generator type ${type} not found`);
            return false;
        }

        const generator = this.gameState.generators[type];

        // Check if generator is unlocked and owned
        if (!generator.unlocked || generator.count === 0) {
            return false;
        }

        const upgradeCost = this.calculateUpgradeCost(type);

        // Check if player can afford the upgrade
        if (!this.canAffordResourceCost(upgradeCost)) {
            return false;
        }

        // Deduct cost and upgrade
        if (!this.deductResourceCost(upgradeCost)) {
            return false;
        }

        generator.upgrades.level += 1;
        generator.upgrades.productionMultiplier = 1 + (generator.upgrades.level * 0.5);

        // Recalculate resource rates
        this.updateResourceRates();

        // Validate state after upgrade
        this.validateGameState();

        return true;
    }

    /**
     * Calculate cost for purchasing generators
     * Uses exponential scaling based on current count
     * Requirements: 7.4
     */
    calculateGeneratorCost(type, quantity = 1) {
        const generator = this.gameState.generators[type];
        const baseCost = generator.baseCost;
        const currentCount = generator.count;
        const multiplier = generator.costMultiplier;

        // Calculate total cost for each resource type
        const totalCost = {};

        for (const resourceType in baseCost) {
            const resourceBaseCost = baseCost[resourceType];
            let resourceTotalCost = 0;

            for (let i = 0; i < quantity; i++) {
                resourceTotalCost += resourceBaseCost * Math.pow(multiplier, currentCount + i);
            }

            totalCost[resourceType] = Math.floor(resourceTotalCost);
        }

        return totalCost;
    }

    /**
     * Calculate cost for upgrading a generator
     * Requirements: 7.4
     */
    calculateUpgradeCost(type) {
        const generator = this.gameState.generators[type];
        const baseCost = generator.baseCost;
        const currentLevel = generator.upgrades.level;

        // Calculate upgrade cost for each resource type (5x base cost, exponential scaling)
        const upgradeCost = {};

        for (const resourceType in baseCost) {
            const resourceBaseCost = baseCost[resourceType];
            const baseUpgradeCost = resourceBaseCost * 5; // Upgrade costs 5x base cost
            upgradeCost[resourceType] = Math.floor(baseUpgradeCost * Math.pow(2, currentLevel));
        }

        return upgradeCost;
    }

    /**
     * Calculate production for a specific generator
     */
    calculateGeneratorProduction(type) {
        const generator = this.gameState.generators[type];
        const baseProduction = generator.baseProduction;
        const count = generator.count;
        const upgradeMultiplier = generator.upgrades.productionMultiplier;
        
        return baseProduction * count * upgradeMultiplier;
    }

    /**
     * Update resource generation rates
     * Requirements: 7.1, 7.2, 7.3
     */
    updateResourceRates() {
        // Reset all resource rates
        this.gameState.gdpPerSecond = 0;
        
        for (const resourceType in this.gameState.resourcesPerSecond) {
            this.gameState.resourcesPerSecond[resourceType] = 0;
        }
        
        // Calculate production from all generators
        for (const generatorType in this.gameState.generators) {
            const generator = this.gameState.generators[generatorType];
            const production = this.calculateGeneratorProduction(generatorType);
            const resourceType = generator.resourceType;
            
            // Add to specific resource rate
            if (resourceType && this.gameState.resourcesPerSecond.hasOwnProperty(resourceType)) {
                this.gameState.resourcesPerSecond[resourceType] += production;
            }
            
            // All generators also contribute to GDP
            this.gameState.gdpPerSecond += production;
        }
    }

    /**
     * Start the game loop
     * Requirements: 5.3, 5.4
     */
    startGameLoop() {
        if (this.isRunning) {
            return;
        }
        
        this.isRunning = true;
        this.lastUpdateTime = performance.now();
        this.gameLoop = requestAnimationFrame(this.loop);
    }

    /**
     * Stop the game loop
     */
    stopGameLoop() {
        if (this.gameLoop) {
            cancelAnimationFrame(this.gameLoop);
            this.gameLoop = null;
        }
        this.isRunning = false;
    }

    /**
     * Main game loop with fixed timestep
     * Requirements: 5.3, 5.4
     */
    loop(currentTime) {
        if (!this.isRunning) {
            return;
        }

        const deltaTime = currentTime - this.lastUpdateTime;
        this.lastUpdateTime = currentTime;
        this.accumulator += deltaTime;
        
        // Fixed timestep updates
        while (this.accumulator >= this.fixedTimeStep) {
            this.tick(this.fixedTimeStep);
            this.accumulator -= this.fixedTimeStep;
        }
        
        // Continue the loop
        this.gameLoop = requestAnimationFrame(this.loop);
    }

    /**
     * Game tick - processes one frame of game logic
     * Requirements: 2.2, 5.3, 5.4, 7.3
     */
    tick(deltaTime) {
        // Update total play time
        this.gameState.totalPlayTime += deltaTime;
        
        // Generate GDP from generators
        if (this.gameState.gdpPerSecond > 0) {
            const gdpGained = (this.gameState.gdpPerSecond * deltaTime) / 1000;
            this.gameState.gdp += gdpGained;
            this.gameState.totalGDPEarned += gdpGained;
        }
        
        // Generate secondary resources from generators
        for (const resourceType in this.gameState.resourcesPerSecond) {
            if (this.gameState.resourcesPerSecond[resourceType] > 0) {
                const resourceGained = (this.gameState.resourcesPerSecond[resourceType] * deltaTime) / 1000;
                this.gameState.resources[resourceType] += resourceGained;
            }
        }

        // Check for generator unlocks
        this.checkGeneratorUnlocks();

        // Check if it's time to save the game state
        const currentTime = performance.now();
        if (currentTime - this.lastSaveTime > this.saveInterval) {
            this.saveGameState();
            this.lastSaveTime = currentTime;
        }

        // Validate state after tick
        this.validateGameState();
        
        // Update UI if UI Manager is available
        if (this.uiManager) {
            this.uiManager.updateDisplays();
        }
    }

    /**
     * Validate and correct game state
     * Requirements: 5.1, 5.2
     */
    validateGameState() {
        // Ensure GDP is not negative or NaN
        if (isNaN(this.gameState.gdp) || this.gameState.gdp < 0) {
            console.warn('Invalid GDP detected, correcting to 0');
            this.gameState.gdp = 0;
        }
        
        // Ensure totalGDPEarned is not negative or NaN
        if (isNaN(this.gameState.totalGDPEarned) || this.gameState.totalGDPEarned < 0) {
            console.warn('Invalid totalGDPEarned detected, correcting to 0');
            this.gameState.totalGDPEarned = 0;
        }
        
        // Ensure generator counts are valid
        for (const generatorType in this.gameState.generators) {
            const generator = this.gameState.generators[generatorType];
            
            if (isNaN(generator.count) || generator.count < 0) {
                console.warn(`Invalid generator count for ${generatorType}, correcting to 0`);
                generator.count = 0;
            }
            
            if (isNaN(generator.upgrades.level) || generator.upgrades.level < 0) {
                console.warn(`Invalid upgrade level for ${generatorType}, correcting to 0`);
                generator.upgrades.level = 0;
                generator.upgrades.productionMultiplier = 1;
            }
        }
        
        // Ensure totalClicks is valid
        if (isNaN(this.gameState.totalClicks) || this.gameState.totalClicks < 0) {
            console.warn('Invalid totalClicks detected, correcting to 0');
            this.gameState.totalClicks = 0;
        }

        // Ensure all resources are valid and not negative
        for (const resourceType in this.gameState.resources) {
            if (isNaN(this.gameState.resources[resourceType]) || this.gameState.resources[resourceType] < 0) {
                console.warn(`Invalid resource ${resourceType} detected, correcting to 0`);
                this.gameState.resources[resourceType] = 0;
            }
        }

        // Ensure resource rates are valid and not negative
        for (const resourceType in this.gameState.resourcesPerSecond) {
            if (isNaN(this.gameState.resourcesPerSecond[resourceType]) || this.gameState.resourcesPerSecond[resourceType] < 0) {
                console.warn(`Invalid resource rate ${resourceType} detected, correcting to 0`);
                this.gameState.resourcesPerSecond[resourceType] = 0;
            }
        }

        // Recalculate resource rates to ensure consistency
        this.updateResourceRates();
    }

    /**
     * Get current game state (for saving)
     */
    getGameState() {
        return JSON.parse(JSON.stringify(this.gameState));
    }

    /**
     * Load game state (from save data)
     */
    loadGameState(state) {
        if (!state || typeof state !== 'object') {
            console.warn('Invalid save state, using default');
            this.gameState = this.getDefaultGameState();
            return;
        }
        
        // Merge with default state to handle missing properties
        this.gameState = { ...this.getDefaultGameState(), ...state };
        
        // Validate loaded state
        this.validateGameState();
        
        // Update calculated values
        this.updateResourceRates();
    }

    /**
     * Reset game to initial state
     */
    resetGame() {
        this.stopGameLoop();
        this.gameState = this.getDefaultGameState();
        this.lastUpdateTime = 0;
        this.accumulator = 0;
    }

    /**
     * Check if player can afford a purchase with GDP
     */
    canAfford(cost) {
        return this.gameState.gdp >= cost;
    }
    
    /**
     * Check if player can afford a resource cost
     * Requirements: 7.4
     */
    canAffordResourceCost(costs) {
        if (!costs) return true;
        
        // Check GDP cost
        if (costs.gdp && this.gameState.gdp < costs.gdp) {
            return false;
        }
        
        // Check resource costs
        for (const resourceType in this.gameState.resources) {
            if (costs[resourceType] && this.gameState.resources[resourceType] < costs[resourceType]) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Deduct resources based on cost
     * Requirements: 7.4
     */
    deductResourceCost(costs) {
        if (!costs) return true;
        
        // Validate we can afford the cost first
        if (!this.canAffordResourceCost(costs)) {
            return false;
        }
        
        // Deduct GDP cost
        if (costs.gdp) {
            this.gameState.gdp -= costs.gdp;
        }
        
        // Deduct resource costs
        for (const resourceType in this.gameState.resources) {
            if (costs[resourceType]) {
                this.gameState.resources[resourceType] -= costs[resourceType];
            }
        }
        
        return true;
    }

    /**
     * Get generator information for UI
     * Requirements: 7.2, 7.3
     */
    getGeneratorInfo(type) {
        if (!this.gameState.generators[type]) {
            return null;
        }

        const generator = this.gameState.generators[type];
        const cost = this.calculateGeneratorCost(type);
        const upgradeCost = this.calculateUpgradeCost(type);

        return {
            count: generator.count,
            cost: cost,
            production: this.calculateGeneratorProduction(type),
            upgradeLevel: generator.upgrades.level,
            upgradeCost: upgradeCost,
            canAffordPurchase: this.canAffordResourceCost(cost),
            canAffordUpgrade: generator.count > 0 && this.canAffordResourceCost(upgradeCost),
            resourceType: generator.resourceType,
            unlocked: generator.unlocked,
            unlockRequirement: generator.unlockRequirement
        };
    }
    
    /**
     * Get resource information for UI
     * Requirements: 7.1, 7.2
     */
    getResourceInfo(resourceType) {
        if (!this.gameState.resources.hasOwnProperty(resourceType)) {
            return null;
        }
        
        return {
            amount: this.gameState.resources[resourceType],
            perSecond: this.gameState.resourcesPerSecond[resourceType]
        };
    }
    
    /**
     * Get all resources information
     * Requirements: 7.1, 7.2
     */
    getAllResourcesInfo() {
        const resources = {};

        // Add GDP as a special resource
        resources.gdp = {
            amount: this.gameState.gdp,
            perSecond: this.gameState.gdpPerSecond,
            totalEarned: this.gameState.totalGDPEarned
        };

        // Add all other resources
        for (const resourceType in this.gameState.resources) {
            resources[resourceType] = this.getResourceInfo(resourceType);
        }

        return resources;
    }

    /**
     * Unlock a generator (used by technology system)
     * Requirements: 7.1
     */
    unlockGenerator(type) {
        if (!this.gameState.generators[type]) {
            console.error(`Generator type ${type} not found`);
            return false;
        }

        this.gameState.generators[type].unlocked = true;
        console.log(`Generator ${type} unlocked!`);
        return true;
    }

    /**
     * Get all unlocked generators
     * Requirements: 7.1, 7.2
     */
    getUnlockedGenerators() {
        const unlockedGenerators = {};

        for (const generatorType in this.gameState.generators) {
            const generator = this.gameState.generators[generatorType];
            if (generator.unlocked) {
                unlockedGenerators[generatorType] = this.getGeneratorInfo(generatorType);
            }
        }

        return unlockedGenerators;
    }

    /**
     * Check and unlock generators based on resource thresholds (temporary until tech system)
     * Requirements: 7.1
     */
    checkGeneratorUnlocks() {
        const gameState = this.gameState;

        // Unlock factory when player has 50 GDP
        if (!gameState.generators.factory.unlocked && gameState.gdp >= 50) {
            this.unlockGenerator('factory');
        }

        // Unlock school when player has 10 food and 5 industry
        if (!gameState.generators.school.unlocked &&
            gameState.resources.food >= 10 &&
            gameState.resources.industry >= 5) {
            this.unlockGenerator('school');
        }

        // Unlock embassy when player has 50 knowledge and 50 industry
        if (!gameState.generators.embassy.unlocked &&
            gameState.resources.knowledge >= 50 &&
            gameState.resources.industry >= 50) {
            this.unlockGenerator('embassy');
        }

        // Unlock bitcoin miner when player has 200 knowledge and 500 industry
        if (!gameState.generators.bitcoinMiner.unlocked &&
            gameState.resources.knowledge >= 200 &&
            gameState.resources.industry >= 500) {
            this.unlockGenerator('bitcoinMiner');
        }
    }
    
    /**
     * Save game state using Orange SDK or localStorage
     * Requirements: 3.2, 3.3
     */
    saveGameState() {
        try {
            const gameState = this.getGameState();
            
            // Try to use Orange SDK if available
            if (window.GGSDK && typeof window.GGSDK.saveGameData === 'function') {
                window.GGSDK.saveGameData(gameState);
                console.log('Game state saved using Orange SDK');
            } else {
                // Use localStorage as fallback
                localStorage.setItem('pooristan_save', JSON.stringify(gameState));
                console.log('Game state saved to localStorage');
            }
            
            return true;
        } catch (error) {
            console.error('Failed to save game state:', error);
            return false;
        }
    }
    
    /**
     * Load game state from Orange SDK or localStorage
     * Requirements: 3.3, 3.4, 3.5
     */
    loadSavedGameState() {
        try {
            // Try to use Orange SDK if available
            if (window.GGSDK && typeof window.GGSDK.getGameData === 'function') {
                const defaultData = this.getDefaultGameState();
                window.GGSDK.getGameData(defaultData, (data) => {
                    this.loadGameState(data);
                    console.log('Game state loaded from Orange SDK');
                });
                return true;
            } else {
                // Use localStorage as fallback
                const savedState = localStorage.getItem('pooristan_save');
                if (savedState) {
                    const parsedState = JSON.parse(savedState);
                    this.loadGameState(parsedState);
                    console.log('Game state loaded from localStorage');
                    return true;
                }
            }
            
            // No saved state found
            console.log('No saved game state found, using default');
            return false;
        } catch (error) {
            console.error('Failed to load game state:', error);
            this.gameState = this.getDefaultGameState();
            return false;
        }
    }
    
    /**
     * Purchase multiple generators at once
     * Requirements: 2.1, 2.5
     */
    purchaseMultipleGenerators(type, quantity) {
        if (quantity <= 0) {
            return false;
        }
        
        return this.purchaseGenerator(type, quantity);
    }
    
    /**
     * Get maximum number of generators that can be purchased with current GDP
     * Requirements: 2.1, 2.5
     */
    getMaxAffordableGenerators(type) {
        if (!this.gameState.generators[type]) {
            return 0;
        }
        
        const currentGDP = this.gameState.gdp;
        if (currentGDP < this.calculateGeneratorCost(type, 1)) {
            return 0;
        }
        
        // Binary search to find maximum affordable quantity
        let low = 1;
        let high = 1000; // Reasonable upper limit
        let result = 0;
        
        while (low <= high) {
            const mid = Math.floor((low + high) / 2);
            const cost = this.calculateGeneratorCost(type, mid);
            
            if (cost <= currentGDP) {
                result = mid;
                low = mid + 1;
            } else {
                high = mid - 1;
            }
        }
        
        return result;
    }
    

}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameEngine;
}