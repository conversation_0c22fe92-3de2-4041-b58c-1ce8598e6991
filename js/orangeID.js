/**
 * OrangeID.js - Orange ID Authentication Module
 * Handles user authentication through Orange ID system using Bedrock Passport
 */

class OrangeIDManager {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
        this.authCallbacks = [];
        this.bedrockConfig = {
            baseUrl: 'https://api.bedrockpassport.com',
            authCallbackUrl: window.location.origin,
            tenantId: 'orange-abc123', // This should be replaced with actual tenant ID
            subscriptionKey: 'your_API_Key' // This should be replaced with actual API key
        };
        this.bedrockProvider = null;
        this.loginStatus = null;
    }

    /**
     * Initialize Orange ID authentication
     */
    async initializeAuth() {
        console.log('OrangeID: Initializing authentication...');
        
        try {
            // Check if we're on the login page and if required libraries are loaded
            if (this.isOnLoginPage()) {
                await this.loadBedrockDependencies();
                this.setupBedrockWidget();
            }
            
            // Check for existing session
            const savedUser = StorageUtils.load('orangeid_user', null);
            if (savedUser && this.validateUserSession(savedUser)) {
                this.currentUser = savedUser;
                console.log('OrangeID: Found valid existing session for user:', savedUser.name);
                this.triggerAuthCallbacks(savedUser);
            }
            
            // Handle authentication callback if present
            this.handleAuthCallback();
            
            this.isInitialized = true;
            return true;
        } catch (error) {
            console.error('OrangeID: Authentication initialization failed:', error);
            this.displayError('Failed to initialize authentication system');
            return false;
        }
    }

    /**
     * Check if we're on the login page
     */
    isOnLoginPage() {
        return window.location.pathname.includes('login.html') || 
               document.getElementById('bedrock-login-widget') !== null;
    }

    /**
     * Load Bedrock Passport dependencies
     */
    async loadBedrockDependencies() {
        return new Promise((resolve, reject) => {
            // Check if dependencies are already loaded
            if (window.React && window.ReactDOM && window.Bedrock) {
                console.log('OrangeID: Dependencies already loaded');
                resolve();
                return;
            }

            console.log('OrangeID: Loading dependencies...');
            let scriptsLoaded = 0;
            const totalScripts = 3;
            
            const checkComplete = () => {
                scriptsLoaded++;
                console.log(`OrangeID: Loaded ${scriptsLoaded}/${totalScripts} scripts`);
                
                if (scriptsLoaded === totalScripts) {
                    // Add a small delay to ensure all scripts are fully initialized
                    setTimeout(() => {
                        if (window.React && window.ReactDOM && window.Bedrock) {
                            console.log('OrangeID: All dependencies loaded successfully');
                            resolve();
                        } else {
                            console.error('OrangeID: Dependencies not available after loading');
                            reject(new Error('Failed to load required libraries'));
                        }
                    }, 100);
                }
            };

            const handleError = (scriptName) => {
                return () => {
                    console.error(`OrangeID: Failed to load ${scriptName}`);
                    reject(new Error(`Failed to load ${scriptName}`));
                };
            };

            // Load React
            if (!window.React) {
                const reactScript = document.createElement('script');
                reactScript.src = 'https://unpkg.com/react@18/umd/react.production.min.js';
                reactScript.crossOrigin = 'anonymous';
                reactScript.onload = checkComplete;
                reactScript.onerror = handleError('React');
                document.head.appendChild(reactScript);
            } else {
                checkComplete();
            }

            // Load ReactDOM
            if (!window.ReactDOM) {
                const reactDOMScript = document.createElement('script');
                reactDOMScript.src = 'https://unpkg.com/react-dom@18/umd/react-dom.production.min.js';
                reactDOMScript.crossOrigin = 'anonymous';
                reactDOMScript.onload = checkComplete;
                reactDOMScript.onerror = handleError('ReactDOM');
                document.head.appendChild(reactDOMScript);
            } else {
                checkComplete();
            }

            // Load Bedrock Passport
            if (!window.Bedrock) {
                const bedrockScript = document.createElement('script');
                bedrockScript.src = 'https://public-cdn-files.pages.dev/bedrock-passport.umd.js';
                bedrockScript.crossOrigin = 'anonymous';
                bedrockScript.onload = checkComplete;
                bedrockScript.onerror = handleError('Bedrock Passport');
                document.head.appendChild(bedrockScript);
            } else {
                checkComplete();
            }
        });
    }

    /**
     * Setup Bedrock widget on login page
     */
    setupBedrockWidget() {
        if (!window.React || !window.ReactDOM || !window.Bedrock) {
            console.error('OrangeID: Required libraries not loaded');
            this.displayError('Authentication system not available');
            return;
        }

        const container = document.getElementById('bedrock-login-widget');
        if (!container) {
            console.warn('OrangeID: Login widget container not found');
            return;
        }

        try {
            // Check if we're handling a callback
            const params = new URLSearchParams(window.location.search);
            const token = params.get('token');
            const refreshToken = params.get('refreshToken');

            // Try to use createRoot (React 18+), fallback to render (React 17-)
            let root;
            if (window.ReactDOM.createRoot) {
                root = window.ReactDOM.createRoot(container);
                if (token && refreshToken) {
                    this.renderCallbackProcessor(root, token, refreshToken);
                } else {
                    this.renderLoginPanel(root);
                }
            } else if (window.ReactDOM.render) {
                // Fallback for older React versions
                if (token && refreshToken) {
                    this.renderCallbackProcessorLegacy(container, token, refreshToken);
                } else {
                    this.renderLoginPanelLegacy(container);
                }
            } else {
                throw new Error('No compatible ReactDOM render method found');
            }
        } catch (error) {
            console.error('OrangeID: Failed to setup Bedrock widget:', error);
            this.displayError('Failed to initialize login widget');
        }
    }

    /**
     * Render the login panel
     */
    renderLoginPanel(root) {
        const loginPanel = window.React.createElement(window.Bedrock.LoginPanel, {
            title: 'Sign in to',
            logo: 'https://irp.cdn-website.com/e81c109a/dms3rep/multi/orange-web3-logo-v2a-20241018.svg',
            logoAlt: 'Orange Web3',
            walletButtonText: 'Connect Wallet',
            showConnectWallet: false,
            separatorText: 'OR',
            features: {
                enableWalletConnect: false,
                enableAppleLogin: true,
                enableGoogleLogin: true,
                enableEmailLogin: false,
            },
            titleClass: 'text-xl font-bold',
            logoClass: 'ml-2 md:h-8 h-6',
            panelClass: 'container p-2 md:p-8 rounded-2xl max-w-[480px]',
            buttonClass: 'hover:border-orange-500',
            separatorTextClass: 'bg-orange-900 text-gray-500',
            separatorClass: 'bg-orange-900',
            linkRowClass: 'justify-center',
            headerClass: 'justify-center',
        });

        const provider = window.React.createElement(
            window.Bedrock.BedrockPassportProvider,
            this.bedrockConfig,
            loginPanel
        );

        root.render(provider);
    }

    /**
     * Render callback processor
     */
    renderCallbackProcessor(root, token, refreshToken) {
        const self = this; // Capture 'this' context for use in React component
        
        const AuthCallbackProcessor = () => {
            const { loginCallback } = window.Bedrock.useBedrockPassport();
            const [message, setMessage] = window.React.useState('Processing authentication...');

            window.React.useEffect(() => {
                const processLogin = async () => {
                    try {
                        setMessage('Verifying tokens...');
                        const success = await loginCallback(token, refreshToken);

                        if (success) {
                            setMessage('Login successful! Redirecting...');
                            
                            // Process the authentication success
                            const userData = await self.validateTokensAndGetUser(token, refreshToken);
                            if (userData) {
                                self.handleSuccessfulAuth(userData);
                            }
                            
                            // Clean URL and redirect
                            window.history.replaceState({}, document.title, window.location.pathname);
                            setTimeout(() => {
                                window.location.href = 'index.html';
                            }, 1000);
                        } else {
                            setMessage('Authentication failed. Please try again.');
                            self.displayError('Authentication failed');
                        }
                    } catch (error) {
                        console.error('OrangeID: Login callback error:', error);
                        setMessage('An error occurred during login.');
                        self.displayError('Login processing failed');
                    }
                };

                processLogin();
            }, [loginCallback]);

            return window.React.createElement(
                'div',
                { style: { textAlign: 'center', padding: '1rem' } },
                message
            );
        };

        const provider = window.React.createElement(
            window.Bedrock.BedrockPassportProvider,
            this.bedrockConfig,
            window.React.createElement(AuthCallbackProcessor)
        );

        root.render(provider);
    }

    /**
     * Handle authentication callback from URL parameters
     */
    handleAuthCallback() {
        const params = new URLSearchParams(window.location.search);
        const token = params.get('token');
        const refreshToken = params.get('refreshToken');
        const error = params.get('error');

        if (error) {
            console.error('OrangeID: Authentication error:', error);
            this.displayError('Authentication failed: ' + error);
            return;
        }

        if (token && refreshToken && !this.isOnLoginPage()) {
            // Handle callback on non-login pages
            console.log('OrangeID: Processing authentication callback');
            this.processAuthTokens(token, refreshToken);
        }
    }

    /**
     * Process authentication tokens
     */
    async processAuthTokens(token, refreshToken) {
        try {
            // This would typically involve validating tokens with the backend
            // For now, we'll simulate the process
            console.log('OrangeID: Processing authentication tokens');
            
            // In a real implementation, you would validate these tokens
            // and retrieve user information from the backend
            const userData = await this.validateTokensAndGetUser(token, refreshToken);
            
            if (userData) {
                this.handleSuccessfulAuth(userData);
                // Clean URL parameters
                window.history.replaceState({}, document.title, window.location.pathname);
            } else {
                throw new Error('Token validation failed');
            }
        } catch (error) {
            console.error('OrangeID: Token processing failed:', error);
            this.displayError('Authentication processing failed');
        }
    }

    /**
     * Validate tokens and get user data (placeholder implementation)
     */
    async validateTokensAndGetUser(token, refreshToken) {
        // This is a placeholder - in real implementation, this would make API calls
        // to validate tokens and retrieve user data
        try {
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Return mock user data for development
            return {
                id: 'user_' + Date.now(),
                email: '<EMAIL>',
                name: 'Test User',
                displayName: 'TestUser',
                picture: 'https://via.placeholder.com/64',
                provider: 'google',
                createdAt: new Date().toISOString(),
                loginTime: Date.now()
            };
        } catch (error) {
            console.error('OrangeID: User data retrieval failed:', error);
            return null;
        }
    }

    /**
     * Handle successful authentication
     */
    handleSuccessfulAuth(userData) {
        console.log('OrangeID: Authentication successful for user:', userData.name);
        
        // Store user data
        this.currentUser = userData;
        StorageUtils.save('orangeid_user', userData);
        
        // Trigger auth callbacks
        this.triggerAuthCallbacks(userData);
        
        // Display success message
        this.displaySuccess(`Welcome, ${userData.name}!`);
        
        // Redirect to main game if on login page
        if (this.isOnLoginPage()) {
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        }
    }

    /**
     * Validate user session
     */
    validateUserSession(userData) {
        if (!userData || typeof userData !== 'object') {
            return false;
        }
        
        // Check required fields
        const requiredFields = ['id', 'name', 'loginTime'];
        for (const field of requiredFields) {
            if (!userData[field]) {
                console.warn(`OrangeID: Missing required field: ${field}`);
                return false;
            }
        }
        
        // Check if session is not too old (24 hours)
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        const sessionAge = Date.now() - userData.loginTime;
        
        if (sessionAge > maxAge) {
            console.log('OrangeID: Session expired');
            return false;
        }
        
        return true;
    }

    /**
     * Display error message
     */
    displayError(message) {
        console.error('OrangeID Error:', message);
        
        const statusElement = document.getElementById('login-status');
        if (statusElement) {
            statusElement.className = 'login-status error';
            statusElement.textContent = message;
            statusElement.style.display = 'block';
        }
        
        // Also try to display in any error containers
        const errorContainers = document.querySelectorAll('.auth-error, .error-message');
        errorContainers.forEach(container => {
            container.textContent = message;
            container.style.display = 'block';
        });
    }

    /**
     * Display success message
     */
    displaySuccess(message) {
        console.log('OrangeID Success:', message);
        
        const statusElement = document.getElementById('login-status');
        if (statusElement) {
            statusElement.className = 'login-status success';
            statusElement.textContent = message;
            statusElement.style.display = 'block';
        }
    }

    /**
     * Clear status messages
     */
    clearStatus() {
        const statusElement = document.getElementById('login-status');
        if (statusElement) {
            statusElement.textContent = '';
            statusElement.style.display = 'none';
            statusElement.className = 'login-status';
        }
    }

    /**
     * Sign out user
     */
    async signOut() {
        console.log('OrangeID: Signing out user');
        
        try {
            // If using Bedrock, call their signOut method
            if (window.Bedrock && this.bedrockProvider) {
                const { signOut } = window.Bedrock.useBedrockPassport();
                if (signOut) {
                    await signOut();
                }
            }
        } catch (error) {
            console.warn('OrangeID: Bedrock signOut failed:', error);
        }
        
        // Clear local session data
        this.currentUser = null;
        StorageUtils.remove('orangeid_user');
        
        // Clear any other session data
        StorageUtils.remove(GAME_CONFIG.AUTO_SAVE_KEY);
        
        // Trigger auth callbacks with null user
        this.triggerAuthCallbacks(null);
        
        console.log('OrangeID: User signed out successfully');
        
        // Redirect to login page if not already there
        if (!this.isOnLoginPage()) {
            window.location.href = 'login.html';
        }
    }

    /**
     * Get current authenticated user
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return this.currentUser !== null;
    }

    /**
     * Get user display name
     */
    getUserDisplayName() {
        return this.currentUser ? this.currentUser.name : 'Guest';
    }

    /**
     * Add authentication callback
     */
    onAuthStateChanged(callback) {
        this.authCallbacks.push(callback);
    }

    /**
     * Render login panel using legacy ReactDOM.render
     */
    renderLoginPanelLegacy(container) {
        const loginPanel = window.React.createElement(window.Bedrock.LoginPanel, {
            title: 'Sign in to',
            logo: 'https://irp.cdn-website.com/e81c109a/dms3rep/multi/orange-web3-logo-v2a-20241018.svg',
            logoAlt: 'Orange Web3',
            walletButtonText: 'Connect Wallet',
            showConnectWallet: false,
            separatorText: 'OR',
            features: {
                enableWalletConnect: false,
                enableAppleLogin: true,
                enableGoogleLogin: true,
                enableEmailLogin: false,
            },
            titleClass: 'text-xl font-bold',
            logoClass: 'ml-2 md:h-8 h-6',
            panelClass: 'container p-2 md:p-8 rounded-2xl max-w-[480px]',
            buttonClass: 'hover:border-orange-500',
            separatorTextClass: 'bg-orange-900 text-gray-500',
            separatorClass: 'bg-orange-900',
            linkRowClass: 'justify-center',
            headerClass: 'justify-center',
        });

        const provider = window.React.createElement(
            window.Bedrock.BedrockPassportProvider,
            this.bedrockConfig,
            loginPanel
        );

        window.ReactDOM.render(provider, container);
    }

    /**
     * Render callback processor using legacy ReactDOM.render
     */
    renderCallbackProcessorLegacy(container, token, refreshToken) {
        const self = this;
        
        const AuthCallbackProcessor = () => {
            const { loginCallback } = window.Bedrock.useBedrockPassport();
            const [message, setMessage] = window.React.useState('Processing authentication...');

            window.React.useEffect(() => {
                const processLogin = async () => {
                    try {
                        setMessage('Verifying tokens...');
                        const success = await loginCallback(token, refreshToken);

                        if (success) {
                            setMessage('Login successful! Redirecting...');
                            
                            const userData = await self.validateTokensAndGetUser(token, refreshToken);
                            if (userData) {
                                self.handleSuccessfulAuth(userData);
                            }
                            
                            window.history.replaceState({}, document.title, window.location.pathname);
                            setTimeout(() => {
                                window.location.href = 'index.html';
                            }, 1000);
                        } else {
                            setMessage('Authentication failed. Please try again.');
                            self.displayError('Authentication failed');
                        }
                    } catch (error) {
                        console.error('OrangeID: Login callback error:', error);
                        setMessage('An error occurred during login.');
                        self.displayError('Login processing failed');
                    }
                };

                processLogin();
            }, [loginCallback]);

            return window.React.createElement(
                'div',
                { style: { textAlign: 'center', padding: '1rem' } },
                message
            );
        };

        const provider = window.React.createElement(
            window.Bedrock.BedrockPassportProvider,
            this.bedrockConfig,
            window.React.createElement(AuthCallbackProcessor)
        );

        window.ReactDOM.render(provider, container);
    }

    /**
     * Create fallback authentication UI when Bedrock is not available
     */
    createFallbackAuthUI() {
        const container = document.getElementById('bedrock-login-widget');
        if (!container) return;

        container.innerHTML = `
            <div class="fallback-auth">
                <h3>Authentication System</h3>
                <p>External authentication service is currently unavailable.</p>
                <div class="fallback-buttons">
                    <button id="fallback-google" class="login-btn google-btn">
                        <span class="btn-icon">G</span>
                        Sign in with Google (Demo)
                    </button>
                    <button id="fallback-apple" class="login-btn apple-btn">
                        <span class="btn-icon">🍎</span>
                        Sign in with Apple (Demo)
                    </button>
                </div>
                <p class="fallback-note">Demo mode - authentication will be simulated</p>
            </div>
        `;

        // Add event listeners for fallback buttons
        const googleBtn = document.getElementById('fallback-google');
        const appleBtn = document.getElementById('fallback-apple');

        if (googleBtn) {
            googleBtn.addEventListener('click', () => this.handleFallbackLogin('google'));
        }

        if (appleBtn) {
            appleBtn.addEventListener('click', () => this.handleFallbackLogin('apple'));
        }
    }

    /**
     * Handle fallback authentication
     */
    async handleFallbackLogin(provider) {
        try {
            this.displaySuccess('Simulating authentication...');
            
            // Simulate authentication delay
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            const mockUser = {
                id: `${provider}_${Date.now()}`,
                email: `demo@${provider}.com`,
                name: `Demo User (${provider})`,
                displayName: `DemoUser`,
                picture: `https://via.placeholder.com/64?text=${provider.charAt(0).toUpperCase()}`,
                provider: provider,
                createdAt: new Date().toISOString(),
                loginTime: Date.now()
            };
            
            this.handleSuccessfulAuth(mockUser);
        } catch (error) {
            console.error('OrangeID: Fallback login failed:', error);
            this.displayError('Authentication failed');
        }
    }

    /**
     * Initialize authentication with fallback support
     */
    async initializeAuthWithFallback() {
        console.log('OrangeID: Initializing authentication with fallback support...');
        
        try {
            // Check for existing session first
            const savedUser = StorageUtils.load('orangeid_user', null);
            if (savedUser && this.validateUserSession(savedUser)) {
                this.currentUser = savedUser;
                console.log('OrangeID: Found valid existing session for user:', savedUser.name);
                this.triggerAuthCallbacks(savedUser);
                this.isInitialized = true;
                return true;
            }
            
            // Handle authentication callback if present
            this.handleAuthCallback();
            
            // If on login page, try to setup authentication UI
            if (this.isOnLoginPage()) {
                try {
                    await this.loadBedrockDependencies();
                    this.setupBedrockWidget();
                } catch (error) {
                    console.warn('OrangeID: Bedrock setup failed, using fallback:', error);
                    this.createFallbackAuthUI();
                }
            }
            
            this.isInitialized = true;
            return true;
        } catch (error) {
            console.error('OrangeID: Authentication initialization failed:', error);
            
            // Create fallback UI if on login page
            if (this.isOnLoginPage()) {
                this.createFallbackAuthUI();
            }
            
            this.displayError('Authentication system initialized with limited functionality');
            this.isInitialized = true;
            return false;
        }
    }

    /**
     * Trigger authentication callbacks
     */
    triggerAuthCallbacks(user) {
        this.authCallbacks.forEach(callback => {
            try {
                callback(user);
            } catch (error) {
                console.error('OrangeID: Auth callback error:', error);
            }
        });
    }
}

// Export for global access
window.OrangeIDManager = OrangeIDManager;

// Create and expose global instance
window.orangeIDInstance = null;

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OrangeIDManager;
}