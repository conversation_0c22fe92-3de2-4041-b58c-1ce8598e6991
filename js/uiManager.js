/**
 * UIManager.js - User Interface Management Module
 * Handles all DOM manipulation and user interface updates
 * Requirements: 4.1, 4.2, 4.3, 4.4, 4.5
 */

class UIManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.orangeSDK = null;
        this.elements = {};
        this.currentTab = 'resources';
        this.updateInterval = null;
        this.lastUpdateTime = 0;
        this.updateFrequency = 100; // Update UI every 100ms
    }

    /**
     * Set Orange SDK reference
     */
    setOrangeSDK(orangeSDK) {
        this.orangeSDK = orangeSDK;
    }

    /**
     * Initialize UI Manager
     * Requirements: 4.1, 4.2, 4.3, 4.4, 4.5
     */
    initialize() {
        console.log('UIManager: Initializing...');
        
        // Cache DOM elements
        this.cacheElements();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Initialize displays
        this.updateAllDisplays();
        
        // Start periodic updates
        this.startPeriodicUpdates();
        
        console.log('UIManager: Initialized successfully');
    }

    /**
     * Cache frequently used DOM elements
     */
    cacheElements() {
        this.elements = {
            // GDP elements
            gdpAmount: DOMUtils.getElementById('gdp-amount'),
            gdpPerSecond: DOMUtils.getElementById('gdp-per-second'),
            gdpClickBtn: DOMUtils.getElementById('gdp-click-btn'),
            
            // User info
            userName: DOMUtils.getElementById('user-name'),
            userInfo: DOMUtils.getElementById('user-info'),
            
            // Tab elements
            tabButtons: document.querySelectorAll('.tab-btn'),
            tabPanels: document.querySelectorAll('.tab-panel'),
            
            // Content areas
            resourceList: DOMUtils.getElementById('resource-list'),
            generatorList: DOMUtils.getElementById('generator-list'),
            eventList: DOMUtils.getElementById('event-list'),
            techTree: DOMUtils.getElementById('tech-tree'),
            
            // Stats
            totalClicks: DOMUtils.getElementById('total-clicks'),
            playTime: DOMUtils.getElementById('play-time'),
            gamePhase: DOMUtils.getElementById('game-phase'),
            
            // Modal
            eventModal: DOMUtils.getElementById('event-modal'),
            eventTitle: DOMUtils.getElementById('event-title'),
            eventDescription: DOMUtils.getElementById('event-description'),
            eventChoices: DOMUtils.getElementById('event-choices'),
            
            // Loading screen
            loadingScreen: DOMUtils.getElementById('loading-screen')
        };
    }

    /**
     * Setup event listeners
     * Requirements: 4.4
     */
    setupEventListeners() {
        // GDP click button
        if (this.elements.gdpClickBtn) {
            this.elements.gdpClickBtn.addEventListener('click', () => {
                this.handleGDPClick();
            });
        }

        // Tab buttons
        this.elements.tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });

        // User info setup
        this.setupUserInfo();
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Space or Enter to click GDP
            if ((e.key === ' ' || e.key === 'Enter') && document.activeElement === document.body) {
                e.preventDefault();
                this.handleGDPClick();
            }
            
            // Number keys 1-4 to switch tabs
            if (e.key >= '1' && e.key <= '4' && document.activeElement === document.body) {
                const tabIndex = parseInt(e.key) - 1;
                if (tabIndex >= 0 && tabIndex < this.elements.tabButtons.length) {
                    this.elements.tabButtons[tabIndex].click();
                }
            }
        });
    }

    /**
     * Setup user information display
     */
    setupUserInfo() {
        // Get user from Orange ID if available
        if (typeof OrangeIDManager !== 'undefined') {
            const orangeID = new OrangeIDManager();
            const user = orangeID.getCurrentUser();
            
            if (user && this.elements.userName) {
                DOMUtils.setText(this.elements.userName, user.name || 'Player');
                
                // Add user profile picture if available
                if (user.picture && this.elements.userInfo) {
                    const profilePic = DOMUtils.createElement('img', {
                        className: 'profile-pic',
                        src: user.picture,
                        alt: `${user.name || 'Player'} profile picture`
                    });
                    
                    profilePic.style.width = '32px';
                    profilePic.style.height = '32px';
                    profilePic.style.borderRadius = '50%';
                    profilePic.style.marginRight = '0.5rem';
                    
                    // Check if profile pic already exists
                    const existingPic = this.elements.userInfo.querySelector('.profile-pic');
                    if (!existingPic) {
                        this.elements.userInfo.insertBefore(profilePic, this.elements.userName);
                    }
                }
            }
        }
    }

    /**
     * Handle GDP click
     * Requirements: 1.1, 1.2, 1.3, 4.4
     */
    handleGDPClick() {
        if (this.gameEngine) {
            const newGDP = this.gameEngine.clickGDP();
            
            if (newGDP !== undefined) {
                // Add click animation
                DOMUtils.addClass(this.elements.gdpClickBtn, 'click-animation', 300);
                
                // Create floating number animation
                this.createFloatingNumber('+1', this.elements.gdpClickBtn);
                
                // Update display immediately
                this.updateGDPDisplay();
                this.updateGameStats();
            }
        }
    }

    /**
     * Create floating number animation
     * Requirements: 4.4
     */
    createFloatingNumber(text, targetElement) {
        if (!targetElement) return;
        
        const rect = targetElement.getBoundingClientRect();
        const floatingNumber = DOMUtils.createElement('div', {
            className: 'floating-number'
        }, text);
        
        // Style the floating number
        Object.assign(floatingNumber.style, {
            position: 'absolute',
            left: `${rect.left + rect.width / 2}px`,
            top: `${rect.top + rect.height / 2}px`,
            transform: 'translate(-50%, -50%)',
            color: '#2ecc71',
            fontWeight: 'bold',
            fontSize: '1.2rem',
            pointerEvents: 'none',
            zIndex: '1000',
            textShadow: '0 0 3px rgba(0,0,0,0.5)',
            animation: 'float-up 1s ease-out forwards'
        });
        
        // Add animation keyframes if they don't exist
        if (!document.querySelector('#floating-animation')) {
            const style = document.createElement('style');
            style.id = 'floating-animation';
            style.textContent = `
                @keyframes float-up {
                    0% { opacity: 1; transform: translate(-50%, -50%); }
                    100% { opacity: 0; transform: translate(-50%, -150%); }
                }
            `;
            document.head.appendChild(style);
        }
        
        // Add to document and remove after animation
        document.body.appendChild(floatingNumber);
        setTimeout(() => {
            if (floatingNumber.parentNode) {
                floatingNumber.parentNode.removeChild(floatingNumber);
            }
        }, 1000);
    }

    /**
     * Switch between tabs
     * Requirements: 4.3
     */
    switchTab(tabName) {
        // Update tab buttons
        this.elements.tabButtons.forEach(button => {
            const isActive = button.getAttribute('data-tab') === tabName;
            button.classList.toggle('active', isActive);
        });

        // Update tab panels
        this.elements.tabPanels.forEach(panel => {
            const isActive = panel.id === `${tabName}-tab`;
            panel.classList.toggle('active', isActive);
        });

        this.currentTab = tabName;
        
        // Update the content of the newly activated tab
        this.updateTabContent(tabName);
    }
    
    /**
     * Update content of the current tab
     * Requirements: 4.1, 4.2, 4.3
     */
    updateTabContent(tabName) {
        switch (tabName) {
            case 'resources':
                this.updateResourceDisplays();
                break;
            case 'upgrades':
                this.updateGeneratorDisplays();
                break;
            case 'events':
                // Will be implemented in Task 9
                break;
            case 'tech':
                // Will be implemented in Task 8
                break;
        }
    }

    /**
     * Update GDP display
     * Requirements: 4.1
     */
    updateGDPDisplay() {
        if (!this.gameEngine) return;
        
        const gameState = this.gameEngine.getGameState();
        
        if (this.elements.gdpAmount) {
            DOMUtils.setText(this.elements.gdpAmount, NumberFormatter.format(gameState.gdp));
        }
        
        if (this.elements.gdpPerSecond) {
            DOMUtils.setText(this.elements.gdpPerSecond, NumberFormatter.format(gameState.gdpPerSecond));
        }
    }

    /**
     * Update resource displays
     * Requirements: 4.1, 4.2, 7.1, 7.2
     */
    updateResourceDisplays() {
        if (!this.gameEngine || !this.elements.resourceList) return;
        
        const resources = this.gameEngine.getAllResourcesInfo();
        let resourceHTML = '';
        
        // Display GDP first as the primary resource
        resourceHTML += `
            <div class="resource-item">
                <h4>GDP</h4>
                <div class="resource-details">
                    <div class="resource-amount">
                        <span class="label">Amount:</span>
                        <span class="value">${NumberFormatter.format(resources.gdp.amount)}</span>
                    </div>
                    <div class="resource-rate">
                        <span class="label">Per Second:</span>
                        <span class="value">${NumberFormatter.format(resources.gdp.perSecond)}</span>
                    </div>
                    <div class="resource-total">
                        <span class="label">Total Earned:</span>
                        <span class="value">${NumberFormatter.format(resources.gdp.totalEarned)}</span>
                    </div>
                </div>
            </div>
        `;
        
        // Display secondary resources
        const resourceNames = {
            food: 'Food',
            industry: 'Industry',
            knowledge: 'Knowledge',
            influence: 'Influence',
            bitcoin: 'Bitcoin'
        };
        
        const resourceColors = {
            food: '#27ae60',      // Green
            industry: '#e74c3c',  // Red
            knowledge: '#3498db', // Blue
            influence: '#f39c12', // Orange
            bitcoin: '#f1c40f'    // Yellow
        };
        
        for (const resourceType in resources) {
            // Skip GDP as it's already displayed
            if (resourceType === 'gdp') continue;
            
            const resource = resources[resourceType];
            const displayName = resourceNames[resourceType] || resourceType;
            const color = resourceColors[resourceType] || '#bdc3c7';
            
            resourceHTML += `
                <div class="resource-item" style="border-left-color: ${color};">
                    <h4>${displayName}</h4>
                    <div class="resource-details">
                        <div class="resource-amount">
                            <span class="label">Amount:</span>
                            <span class="value">${NumberFormatter.format(resource.amount)}</span>
                        </div>
                        <div class="resource-rate">
                            <span class="label">Per Second:</span>
                            <span class="value">${NumberFormatter.format(resource.perSecond)}</span>
                        </div>
                    </div>
                </div>
            `;
        }
        
        DOMUtils.setHTML(this.elements.resourceList, resourceHTML);
    }

    /**
     * Update generator displays
     * Requirements: 4.2, 4.3, 4.5, 7.2, 7.3
     */
    updateGeneratorDisplays() {
        if (!this.gameEngine || !this.elements.generatorList) return;

        let generatorHTML = '';

        // Get all unlocked generators
        const unlockedGenerators = this.gameEngine.getUnlockedGenerators();

        // Generator display names and descriptions
        const generatorConfig = {
            farm: {
                name: 'Farm',
                description: 'Produces food and basic GDP',
                icon: '🚜'
            },
            factory: {
                name: 'Factory',
                description: 'Produces industrial goods',
                icon: '🏭'
            },
            school: {
                name: 'School',
                description: 'Generates knowledge through education',
                icon: '🏫'
            },
            embassy: {
                name: 'Embassy',
                description: 'Builds international influence',
                icon: '🏛️'
            },
            bitcoinMiner: {
                name: 'Bitcoin Miner',
                description: 'Mines cryptocurrency for the nation',
                icon: '₿'
            }
        };

        // Display each unlocked generator
        for (const generatorType in unlockedGenerators) {
            const generatorInfo = unlockedGenerators[generatorType];
            const config = generatorConfig[generatorType];

            if (!config) continue;

            // Format cost display
            const costDisplay = this.formatResourceCost(generatorInfo.cost);
            const upgradeCostDisplay = this.formatResourceCost(generatorInfo.upgradeCost);

            generatorHTML += `
                <div class="generator-item">
                    <h4>${config.icon} ${config.name}</h4>
                    <p class="generator-description">${config.description}</p>
                    <div class="generator-details">
                        <div class="generator-count">
                            <span class="label">Owned:</span>
                            <span class="value">${generatorInfo.count}</span>
                        </div>
                        <div class="generator-production">
                            <span class="label">Production:</span>
                            <span class="value">${NumberFormatter.format(generatorInfo.production)} ${generatorInfo.resourceType}/s</span>
                        </div>
                    </div>

                    <div class="generator-purchase">
                        <div class="purchase-info">
                            <span class="label">Cost:</span>
                            <span class="value">${costDisplay}</span>
                        </div>
                        <div class="purchase-buttons">
                            <button class="purchase-btn ${generatorInfo.canAffordPurchase ? '' : 'disabled'}"
                                data-type="${generatorType}" data-quantity="1"
                                ${generatorInfo.canAffordPurchase ? '' : 'disabled'}>
                                Buy 1
                            </button>

                            <button class="purchase-btn ${generatorInfo.canAffordPurchase ? '' : 'disabled'}"
                                data-type="${generatorType}" data-quantity="10"
                                ${generatorInfo.canAffordPurchase ? '' : 'disabled'}>
                                Buy 10
                            </button>

                            <button class="purchase-btn ${generatorInfo.canAffordPurchase ? '' : 'disabled'}"
                                data-type="${generatorType}" data-quantity="max"
                                ${generatorInfo.canAffordPurchase ? '' : 'disabled'}>
                                Buy Max
                            </button>
                        </div>
                    </div>

                    <div class="generator-upgrade">
                        <div class="upgrade-info">
                            <span class="label">Upgrade Level:</span>
                            <span class="value">${generatorInfo.upgradeLevel}</span>
                        </div>
                        <div class="upgrade-cost">
                            <span class="label">Upgrade Cost:</span>
                            <span class="value">${upgradeCostDisplay}</span>
                        </div>
                        <button class="upgrade-btn ${generatorInfo.canAffordUpgrade ? '' : 'disabled'}"
                            data-type="${generatorType}"
                            ${generatorInfo.canAffordUpgrade ? '' : 'disabled'}>
                            Upgrade ${config.name}
                        </button>
                    </div>
                </div>
            `;
        }

        DOMUtils.setHTML(this.elements.generatorList, generatorHTML);

        // Add event listeners to the newly created buttons
        this.setupGeneratorButtons();
    }

    /**
     * Format resource cost for display
     * Requirements: 7.4
     */
    formatResourceCost(cost) {
        if (!cost || typeof cost !== 'object') {
            return '0';
        }

        const costParts = [];
        const resourceNames = {
            gdp: 'GDP',
            food: 'Food',
            industry: 'Industry',
            knowledge: 'Knowledge',
            influence: 'Influence',
            bitcoin: 'Bitcoin'
        };

        for (const resourceType in cost) {
            const amount = cost[resourceType];
            if (amount > 0) {
                const displayName = resourceNames[resourceType] || resourceType;
                costParts.push(`${NumberFormatter.format(amount)} ${displayName}`);
            }
        }

        return costParts.length > 0 ? costParts.join(', ') : '0';
    }
    
    /**
     * Setup generator purchase and upgrade buttons
     * Requirements: 4.3, 4.4, 4.5
     */
    setupGeneratorButtons() {
        // Purchase buttons
        const purchaseButtons = document.querySelectorAll('.purchase-btn');
        purchaseButtons.forEach(button => {
            if (button.getAttribute('data-listener') === 'true') return;
            
            button.setAttribute('data-listener', 'true');
            button.addEventListener('click', () => {
                const type = button.getAttribute('data-type');
                const quantityAttr = button.getAttribute('data-quantity');
                
                let quantity = 1;
                if (quantityAttr === 'max') {
                    quantity = this.gameEngine.getMaxAffordableGenerators(type);
                } else {
                    quantity = parseInt(quantityAttr) || 1;
                }
                
                this.handleGeneratorPurchase(type, quantity);
            });
        });
        
        // Upgrade buttons
        const upgradeButtons = document.querySelectorAll('.upgrade-btn');
        upgradeButtons.forEach(button => {
            if (button.getAttribute('data-listener') === 'true') return;
            
            button.setAttribute('data-listener', 'true');
            button.addEventListener('click', () => {
                const type = button.getAttribute('data-type');
                this.handleGeneratorUpgrade(type);
            });
        });
    }
    
    /**
     * Handle generator purchase
     * Requirements: 2.1, 2.5, 4.4, 4.5
     */
    handleGeneratorPurchase(type, quantity) {
        if (!this.gameEngine) return;
        
        const success = this.gameEngine.purchaseMultipleGenerators(type, quantity);
        
        if (success) {
            // Show purchase animation
            const button = document.querySelector(`.purchase-btn[data-type="${type}"][data-quantity="${quantity}"]`);
            if (button) {
                DOMUtils.addClass(button, 'purchase-animation', 500);
                
                // Create floating text showing how many were purchased
                this.createFloatingNumber(`+${quantity}`, button);
            }
            
            // Play purchase sound (will be implemented in Task 11)
            
            // Update displays
            this.updateAllDisplays();
        } else {
            // Show error notification
            this.showNotification('Cannot afford this purchase!', 'error');
        }
    }
    
    /**
     * Handle generator upgrade
     * Requirements: 2.3, 2.4, 4.4, 4.5
     */
    handleGeneratorUpgrade(type) {
        if (!this.gameEngine) return;
        
        const success = this.gameEngine.upgradeGenerator(type);
        
        if (success) {
            // Show upgrade animation
            const button = document.querySelector(`.upgrade-btn[data-type="${type}"]`);
            if (button) {
                DOMUtils.addClass(button, 'purchase-animation', 500);
                
                // Create floating text showing upgrade
                this.createFloatingNumber('Upgraded!', button);
            }
            
            // Play upgrade sound (will be implemented in Task 11)
            
            // Update displays
            this.updateAllDisplays();
        } else {
            // Show error notification
            this.showNotification('Cannot afford this upgrade!', 'error');
        }
    }

    /**
     * Update game stats
     */
    updateGameStats() {
        if (!this.gameEngine) return;
        
        const gameState = this.gameEngine.getGameState();
        
        if (this.elements.totalClicks) {
            DOMUtils.setText(this.elements.totalClicks, NumberFormatter.format(gameState.totalClicks));
        }
        
        if (this.elements.playTime) {
            DOMUtils.setText(this.elements.playTime, TimeUtils.formatTime(gameState.totalPlayTime));
        }
        
        if (this.elements.gamePhase) {
            DOMUtils.setText(this.elements.gamePhase, gameState.gamePhase || '1');
        }
    }

    /**
     * Update all displays
     * Requirements: 4.1, 4.2, 4.3
     */
    updateAllDisplays() {
        this.updateGDPDisplay();
        this.updateResourceDisplays();
        this.updateGeneratorDisplays();
        this.updateGameStats();
    }

    /**
     * Update displays (called by game engine)
     * Requirements: 4.1, 4.2
     */
    updateDisplays() {
        const now = performance.now();
        
        // Limit update frequency to avoid performance issues
        if (now - this.lastUpdateTime < this.updateFrequency) {
            return;
        }
        
        this.lastUpdateTime = now;
        
        // Only update GDP and stats frequently, other displays less often
        this.updateGDPDisplay();
        this.updateGameStats();
    }

    /**
     * Start periodic UI updates
     */
    startPeriodicUpdates() {
        // Update resource and generator displays every 5 seconds
        this.updateInterval = setInterval(() => {
            this.updateResourceDisplays();
            this.updateGeneratorDisplays();
        }, 5000);
    }

    /**
     * Stop periodic updates
     */
    stopPeriodicUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * Show notification
     * Requirements: 4.4, 4.5
     */
    showNotification(message, type = 'info') {
        // Create notification element if it doesn't exist
        let notification = document.querySelector('.game-notification');
        if (!notification) {
            notification = DOMUtils.createElement('div', {
                className: `game-notification ${type}`
            });
            
            // Style the notification
            Object.assign(notification.style, {
                position: 'fixed',
                bottom: '20px',
                right: '20px',
                padding: '10px 20px',
                borderRadius: '5px',
                color: 'white',
                fontWeight: 'bold',
                zIndex: '1000',
                opacity: '0',
                transition: 'opacity 0.3s ease'
            });
            
            document.body.appendChild(notification);
        } else {
            // Update existing notification
            notification.className = `game-notification ${type}`;
        }
        
        // Set background color based on type
        switch (type) {
            case 'error':
                notification.style.backgroundColor = 'rgba(231, 76, 60, 0.9)';
                break;
            case 'success':
                notification.style.backgroundColor = 'rgba(46, 204, 113, 0.9)';
                break;
            case 'warning':
                notification.style.backgroundColor = 'rgba(243, 156, 18, 0.9)';
                break;
            default:
                notification.style.backgroundColor = 'rgba(52, 152, 219, 0.9)';
        }
        
        // Set message and show notification
        notification.textContent = message;
        notification.style.opacity = '1';
        
        // Hide notification after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            
            // Remove from DOM after fade out
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * Show event modal - placeholder
     * Will be fully implemented in Task 9
     */
    showEventModal(event) {
        console.log('UIManager: Show event modal (placeholder):', event);
    }

    /**
     * Hide event modal - placeholder
     * Will be fully implemented in Task 9
     */
    hideEventModal() {
        console.log('UIManager: Hide event modal (placeholder)');
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        if (this.elements.loadingScreen) {
            this.elements.loadingScreen.classList.remove('hidden');
        }
    }
    
    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        if (this.elements.loadingScreen) {
            this.elements.loadingScreen.classList.add('hidden');
        }
    }

    /**
     * Cleanup UI Manager
     */
    cleanup() {
        this.stopPeriodicUpdates();
        console.log('UIManager: Cleaned up');
    }
}

// Export for global access
window.UIManager = UIManager;

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIManager;
}