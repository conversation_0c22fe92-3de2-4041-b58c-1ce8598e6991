/**
 * Main.js - Application Entry Point
 * Initializes the Pooristan game and coordinates all modules
 */

class PooristanGame {
    constructor() {
        this.gameEngine = null;
        this.uiManager = null;
        this.orangeSDK = null;
        this.orangeID = null;
        this.isInitialized = false;
        this.isAuthenticated = false;
    }

    /**
     * Initialize the game application
     */
    async init() {
        try {
            console.log('Initializing Pooristan game...');
            
            // Show loading screen
            this.showLoadingScreen();
            
            // Check authentication status
            await this.checkAuthentication();
            
            if (!this.isAuthenticated) {
                // Redirect to login if not authenticated
                this.redirectToLogin();
                return;
            }
            
            // Initialize core modules
            await this.initializeModules();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Start the game
            this.startGame();
            
            // Hide loading screen
            this.hideLoadingScreen();
            
            this.isInitialized = true;
            console.log('Pooristan game initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * Check if user is authenticated
     */
    async checkAuthentication() {
        try {
            this.orangeID = new OrangeIDManager();
            this.isAuthenticated = this.orangeID.isAuthenticated();
            
            if (this.isAuthenticated) {
                const user = this.orangeID.getCurrentUser();
                console.log('User authenticated:', user);
            }
        } catch (error) {
            console.error('Authentication check failed:', error);
            this.isAuthenticated = false;
        }
    }

    /**
     * Initialize all game modules
     */
    async initializeModules() {
        // Initialize Orange SDK
        this.orangeSDK = new OrangeSDKManager();
        
        // Initialize Game Engine
        this.gameEngine = new GameEngine(this.orangeSDK);
        
        // Initialize Orange SDK with game engine reference
        await this.orangeSDK.initialize(this.gameEngine);
        
        // Initialize UI Manager
        this.uiManager = new UIManager(this.gameEngine);
        
        // Connect modules
        this.gameEngine.setUIManager(this.uiManager);
        this.uiManager.setOrangeSDK(this.orangeSDK);
        
        // Setup Orange SDK event listeners
        this.setupOrangeSDKEventListeners();
        
        // Load game data
        await this.loadGameData();
        
        // Signal game loaded to Orange SDK
        this.orangeSDK.gameLoaded();
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseGame();
            } else {
                this.resumeGame();
            }
        });

        // Handle window beforeunload
        window.addEventListener('beforeunload', () => {
            this.saveAndQuit();
        });

        // Handle logout
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }

        // Handle errors
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.handleRuntimeError(event.error);
        });
    }

    /**
     * Start the game
     */
    startGame() {
        if (this.gameEngine && this.uiManager) {
            this.gameEngine.startGameLoop();
            this.uiManager.initialize();
            console.log('Game started');
        }
    }

    /**
     * Pause the game
     */
    pauseGame() {
        if (this.gameEngine) {
            this.gameEngine.pauseGame();
        }
        if (this.orangeSDK) {
            this.orangeSDK.gamePaused();
        }
        console.log('Game paused');
    }

    /**
     * Resume the game
     */
    resumeGame() {
        if (this.gameEngine) {
            this.gameEngine.resumeGame();
        }
        if (this.orangeSDK) {
            this.orangeSDK.gameResumed();
        }
        console.log('Game resumed');
    }

    /**
     * Save game and quit
     */
    saveAndQuit() {
        if (this.gameEngine) {
            this.gameEngine.saveGame();
        }
        if (this.orangeSDK) {
            this.orangeSDK.gameQuit();
        }
        console.log('Game saved and quit');
    }

    /**
     * Handle user logout
     */
    logout() {
        try {
            this.saveAndQuit();
            
            if (this.orangeID) {
                this.orangeID.signOut();
            }
            
            this.redirectToLogin();
        } catch (error) {
            console.error('Logout failed:', error);
        }
    }

    /**
     * Redirect to login page
     */
    redirectToLogin() {
        window.location.href = 'login.html';
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
        }
    }

    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
        }
    }

    /**
     * Handle initialization errors
     */
    handleInitializationError(error) {
        console.error('Initialization error:', error);
        
        // Show error message to user
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            const loadingContent = loadingScreen.querySelector('.loading-content');
            if (loadingContent) {
                loadingContent.innerHTML = `
                    <h2>Failed to Load Game</h2>
                    <p>An error occurred while loading Pooristan.</p>
                    <p>Please refresh the page to try again.</p>
                    <button onclick="window.location.reload()" style="
                        padding: 0.75rem 1.5rem;
                        background: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        cursor: pointer;
                        margin-top: 1rem;
                    ">Refresh Page</button>
                `;
            }
        }
    }

    /**
     * Setup Orange SDK event listeners
     */
    setupOrangeSDKEventListeners() {
        if (!this.orangeSDK) return;
        
        // Listen for pause events from Orange SDK
        this.orangeSDK.onPaused(() => {
            console.log('Orange SDK pause event received');
            if (this.gameEngine) {
                this.gameEngine.pauseGame();
            }
        });
        
        // Listen for resume events from Orange SDK
        this.orangeSDK.onResumed(() => {
            console.log('Orange SDK resume event received');
            if (this.gameEngine) {
                this.gameEngine.resumeGame();
            }
        });
        
        // Listen for quit events from Orange SDK
        this.orangeSDK.onQuit(() => {
            console.log('Orange SDK quit event received');
            this.saveAndQuit();
        });
    }

    /**
     * Load game data from Orange SDK or fallback storage
     */
    async loadGameData() {
        return new Promise((resolve) => {
            if (!this.orangeSDK || !this.gameEngine) {
                resolve();
                return;
            }
            
            const defaultData = this.gameEngine.getDefaultGameState();
            
            this.orangeSDK.getGameData(defaultData, (loadedData) => {
                try {
                    this.gameEngine.loadGameState(loadedData);
                    console.log('Game data loaded successfully');
                } catch (error) {
                    console.error('Error loading game data:', error);
                    // Use default state on error
                    this.gameEngine.loadGameState(defaultData);
                }
                resolve();
            });
        });
    }

    /**
     * Handle runtime errors
     */
    handleRuntimeError(error) {
        // Log error for debugging
        console.error('Runtime error:', error);
        
        // Attempt to save game state
        try {
            if (this.gameEngine) {
                this.gameEngine.saveGame();
            }
        } catch (saveError) {
            console.error('Failed to save game after error:', saveError);
        }
        
        // Show user-friendly error message
        // This could be expanded to show a modal or notification
    }
}

// Initialize the game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create global game instance
    window.pooristanGame = new PooristanGame();
    
    // Start initialization
    window.pooristanGame.init();
});

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PooristanGame;
}