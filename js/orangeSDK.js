/**
 * Orange SDK Integration Module
 * Handles all Orange SDK communication, game state persistence, and lifecycle events
 */

class OrangeSDKManager {
    constructor() {
        this.isSDKAvailable = false;
        this.gameEngine = null;
        this.eventListeners = {
            paused: [],
            resumed: [],
            quit: []
        };
        
        // Initialize SDK availability check
        this.checkSDKAvailability();
    }

    /**
     * Check if Orange SDK is available and initialize
     */
    checkSDKAvailability() {
        try {
            this.isSDKAvailable = typeof GGSDK !== 'undefined' && GGSDK !== null;
            if (this.isSDKAvailable) {
                console.log('Orange SDK detected and available');
            } else {
                console.log('Orange SDK not available - running in standalone mode');
            }
        } catch (error) {
            console.error('Error checking SDK availability:', error);
            this.isSDKAvailable = false;
        }
    }

    /**
     * Initialize the Orange SDK integration
     * @param {GameEngine} gameEngine - Reference to the game engine
     */
    async initialize(gameEngine) {
        this.gameEngine = gameEngine;
        
        try {
            if (this.isSDKAvailable) {
                await this.setupSDKEventListeners();
                console.log('Orange SDK initialized successfully');
            } else {
                console.log('Orange SDK not available - running in standalone mode');
            }
        } catch (error) {
            console.error('Error initializing Orange SDK:', error);
            this.isSDKAvailable = false;
        }
    }

    /**
     * Set up Orange SDK event listeners for pause/resume/quit
     */
    setupSDKEventListeners() {
        if (!this.isSDKAvailable) return;

        try {
            // Listen for pause events
            GGSDK.listenPaused(() => {
                console.log('Game paused by Orange SDK');
                this.triggerPauseEvent();
            });

            // Listen for resume events
            GGSDK.listenResumed(() => {
                console.log('Game resumed by Orange SDK');
                this.triggerResumeEvent();
            });

            // Listen for quit events
            GGSDK.listenQuit(() => {
                console.log('Game quit requested by Orange SDK');
                this.triggerQuitEvent();
            });
        } catch (error) {
            console.error('Error setting up SDK event listeners:', error);
            throw error;
        }
    }

    /**
     * Signal to Orange SDK that the game has loaded
     */
    gameLoaded() {
        try {
            if (this.isSDKAvailable) {
                GGSDK.gameLoaded();
                console.log('Game loaded signal sent to Orange SDK');
            } else {
                console.log('Game loaded (SDK not available)');
            }
        } catch (error) {
            console.error('Error signaling game loaded:', error);
        }
    }

    /**
     * Signal to Orange SDK that the game is over
     * @param {number} finalScore - The player's final score
     */
    gameOver(finalScore) {
        try {
            if (this.isSDKAvailable) {
                GGSDK.gameOver(finalScore);
                console.log(`Game over signal sent to Orange SDK with score: ${finalScore}`);
            } else {
                console.log(`Game over (SDK not available) - Final Score: ${finalScore}`);
            }
        } catch (error) {
            console.error('Error signaling game over:', error);
        }
    }

    /**
     * Save game data using Orange SDK
     * @param {Object} gameData - The game state to save
     */
    saveGameData(gameData) {
        if (!this.isSDKAvailable) {
            console.log('Save requested but Orange SDK not available - data not persisted');
            return;
        }

        try {
            // Add timestamp and version info
            const dataToSave = {
                ...gameData,
                lastSaved: Date.now(),
                version: "1.0.0"
            };

            GGSDK.saveGameData(dataToSave);
            console.log('Game data saved via Orange SDK');
        } catch (error) {
            console.error('Error saving game data via Orange SDK:', error);
        }
    }

    /**
     * Load game data using Orange SDK
     * @param {Object} defaultData - Default game state if no save exists
     * @param {Function} callback - Callback function to handle loaded data
     */
    getGameData(defaultData, callback) {
        if (!this.isSDKAvailable) {
            console.log('Load requested but Orange SDK not available - using default data');
            callback(defaultData);
            return;
        }

        try {
            GGSDK.getGameData(defaultData, (loadedData) => {
                try {
                    // Validate loaded data
                    const validatedData = this.validateGameData(loadedData, defaultData);
                    console.log('Game data loaded via Orange SDK');
                    callback(validatedData);
                } catch (error) {
                    console.error('Error validating loaded data:', error);
                    callback(defaultData);
                }
            });
        } catch (error) {
            console.error('Error loading game data via Orange SDK:', error);
            // Return default data on error
            callback(defaultData);
        }
    }

    /**
     * Validate game data integrity and structure
     * @param {Object} data - Data to validate
     * @param {Object} defaultData - Default data structure for comparison
     * @returns {Object} Validated and corrected data
     */
    validateGameData(data, defaultData) {
        try {
            // Check if data exists and has basic structure
            if (!data || typeof data !== 'object') {
                console.warn('Invalid game data structure, using defaults');
                return defaultData;
            }

            // Check version compatibility
            if (data.version && data.version !== defaultData.version) {
                console.warn(`Version mismatch: saved=${data.version}, current=${defaultData.version}`);
                // Could implement migration logic here in the future
            }

            // Validate core properties exist
            const requiredProperties = ['gdp', 'generators', 'resources'];
            for (const prop of requiredProperties) {
                if (!(prop in data)) {
                    console.warn(`Missing required property: ${prop}, using default`);
                    data[prop] = defaultData[prop];
                }
            }

            // Validate numeric values
            if (typeof data.gdp !== 'number' || data.gdp < 0) {
                data.gdp = defaultData.gdp;
            }

            // Validate generators object
            if (!data.generators || typeof data.generators !== 'object') {
                data.generators = defaultData.generators;
            }

            // Validate resources object
            if (!data.resources || typeof data.resources !== 'object') {
                data.resources = defaultData.resources;
            }

            return data;
        } catch (error) {
            console.error('Error validating game data:', error);
            return defaultData;
        }
    }

    /**
     * Add event listener for pause events
     * @param {Function} callback - Function to call when game is paused
     */
    onPaused(callback) {
        this.eventListeners.paused.push(callback);
    }

    /**
     * Add event listener for resume events
     * @param {Function} callback - Function to call when game is resumed
     */
    onResumed(callback) {
        this.eventListeners.resumed.push(callback);
    }

    /**
     * Add event listener for quit events
     * @param {Function} callback - Function to call when game quit is requested
     */
    onQuit(callback) {
        this.eventListeners.quit.push(callback);
    }

    /**
     * Trigger pause event to all listeners
     */
    triggerPauseEvent() {
        this.eventListeners.paused.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('Error in pause event callback:', error);
            }
        });
    }

    /**
     * Trigger resume event to all listeners
     */
    triggerResumeEvent() {
        this.eventListeners.resumed.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('Error in resume event callback:', error);
            }
        });
    }

    /**
     * Trigger quit event to all listeners
     */
    triggerQuitEvent() {
        this.eventListeners.quit.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('Error in quit event callback:', error);
            }
        });
    }

    /**
     * Get SDK availability status
     * @returns {boolean} True if SDK is available
     */
    isSDKReady() {
        return this.isSDKAvailable;
    }

    /**
     * Force save game data (useful for critical moments)
     */
    forceSave() {
        if (this.gameEngine) {
            const gameState = this.gameEngine.getGameState();
            this.saveGameData(gameState);
        }
    }

    /**
     * Signal game paused (for compatibility with main.js)
     */
    gamePaused() {
        console.log('Game paused signal');
        // Orange SDK doesn't need explicit pause signal, it's handled by events
    }

    /**
     * Signal game resumed (for compatibility with main.js)
     */
    gameResumed() {
        console.log('Game resumed signal');
        // Orange SDK doesn't need explicit resume signal, it's handled by events
    }

    /**
     * Signal game quit (for compatibility with main.js)
     */
    gameQuit() {
        console.log('Game quit signal');
        // Save game before quit
        this.forceSave();
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { OrangeSDKManager };
}