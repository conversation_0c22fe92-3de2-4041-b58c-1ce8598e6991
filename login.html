<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pooristan: From Dust to Dominance - Login</title>
    <link rel="stylesheet" href="css/styles.css">
    <!-- Tailwind CSS for Bedrock Passport styling -->
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="login-page">
    <div class="login-container">
        <div class="game-logo">
            <h1>Pooristan</h1>
            <p class="subtitle">From Dust to Dominance</p>
        </div>
        
        <div class="login-form">
            <h2>Sign in with Orange ID</h2>
            
            <!-- Bedrock Passport Widget Container -->
            <div id="bedrock-login-widget"></div>
            
            <!-- Status and Error Messages -->
            <div class="login-status" id="login-status" style="display: none;"></div>
            <div class="auth-error" style="display: none;"></div>
        </div>
        
        <!-- Loading indicator -->
        <div id="loading-indicator" class="loading-indicator" style="display: none;">
            <div class="spinner"></div>
            <p>Loading authentication system...</p>
        </div>
    </div>
    
    <!-- Load utilities first -->
    <script src="js/utils.js"></script>
    <!-- Load Orange ID authentication -->
    <script src="js/orangeID.js"></script>
    
    <script>
        // Initialize Orange ID authentication
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Show loading indicator
                const loadingIndicator = document.getElementById('loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'block';
                }
                
                // Initialize Orange ID authentication
                const orangeID = new OrangeIDManager();
                
                // Store global instance
                window.orangeIDInstance = orangeID;
                
                // DO NOT EVER USE FALLBACKS fallback initialization method for better error handling
                const success = await orangeID.initializeAuthWithFallback();
                
                // Hide loading indicator
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
                
                if (!success) {
                    console.error('Failed to initialize Orange ID authentication');
                    // REMOVE ALL fallbacks
                    const statusElement = document.getElementById('login-status');
                    if (statusElement) {
                        statusElement.className = 'login-status error';
                        statusElement.textContent = 'Authentication system unavailable. Please try again later.';
                        statusElement.style.display = 'block';
                    }
                }
                
                // Set up error handling for authentication failures
                orangeID.onAuthStateChanged((user) => {
                    if (user) {
                        console.log('User authenticated:', user.name);
                    } else {
                        console.log('User signed out');
                    }
                });
                
            } catch (error) {
                console.error('Login page initialization error:', error);
                
                // Hide loading indicator
                const loadingIndicator = document.getElementById('loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
                
                // Show error message
                const statusElement = document.getElementById('login-status');
                if (statusElement) {
                    statusElement.className = 'login-status error';
                    statusElement.textContent = 'Failed to load authentication system.';
                    statusElement.style.display = 'block';
                }
            }
        });
        
        // Handle page visibility changes to refresh authentication state
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // Page became visible, check authentication state
                const orangeID = window.orangeIDInstance;
                if (orangeID && orangeID.isAuthenticated()) {
                    // User is already authenticated, redirect to game
                    window.location.href = 'index.html';
                }
            }
        });
    </script>
</body>
</html>