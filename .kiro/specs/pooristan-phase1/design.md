# Design Document

## Overview

Pooristan: From Dust to Dominance is a comprehensive browser-based idle clicker game built with vanilla HTML, CSS, and JavaScript. The game features a complete economic simulation where players manage multiple resource types, research technologies, handle random events, and work toward victory conditions. The architecture is designed to be modular and extensible across four development phases while integrating with the Orange SDK for tournament platform compatibility and Orange ID for user authentication.

## Architecture

### High-Level Architecture

The game follows a modular architecture with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Presentation  │    │   Game Logic    │    │   Data Layer    │
│     Layer       │◄──►│     Layer       │◄──►│     Layer       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI Manager    │    │  Game Engine    │    │  Orange SDK     │
│   DOM Updates   │    │  Game Loop      │    │  State Manager  │
│   Event Handlers│    │  Resource Mgmt  │    │  Save/Load      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### File Structure

```
pooristan/
├── index.html              # Main game page
├── login.html              # Orange ID authentication page
├── css/
│   └── styles.css          # Game styling
├── js/
│   ├── main.js             # Application entry point
│   ├── gameEngine.js       # Core game logic
│   ├── uiManager.js        # UI updates and interactions
│   ├── orangeSDK.js        # Orange SDK integration
│   ├── orangeID.js         # Orange ID authentication
│   └── utils.js            # Utility functions
└── assets/
    └── images/             # Game images and icons
```

## Components and Interfaces

### 1. Game Engine (`gameEngine.js`)

**Purpose:** Manages core game state, resource generation, and game loop.

**Key Responsibilities:**
- Maintain game state (GDP, generators, upgrades)
- Handle resource generation calculations
- Process player actions (clicks, purchases)
- Manage game timing and intervals

**Interface:**
```javascript
class GameEngine {
  constructor()
  
  // Core game actions
  clickGDP()
  purchaseGenerator(type, quantity)
  upgradeGenerator(type, level)
  
  // Game loop
  startGameLoop()
  stopGameLoop()
  tick()
  
  // State management
  getGameState()
  loadGameState(state)
  resetGame()
}
```

### 2. UI Manager (`uiManager.js`)

**Purpose:** Handles all DOM manipulation and user interface updates.

**Key Responsibilities:**
- Update resource displays
- Handle button states and interactions
- Provide visual feedback for actions
- Manage UI animations and transitions

**Interface:**
```javascript
class UIManager {
  constructor(gameEngine)
  
  // Display updates
  updateGDPDisplay(amount)
  updateGeneratorDisplay(type, count, cost)
  updateUpgradeDisplay(type, level, cost)
  
  // User interactions
  setupEventListeners()
  handleGDPClick()
  handlePurchase(type)
  handleUpgrade(type)
  
  // Visual feedback
  showPurchaseAnimation(type)
  showClickAnimation()
  updateButtonStates()
}
```

### 3. Orange SDK Integration (`orangeSDK.js`)

**Purpose:** Manages integration with the Orange Games SDK for tournament platform compatibility.

**Key Responsibilities:**
- Handle game state persistence
- Communicate with tournament platform
- Manage game lifecycle events

**Interface:**
```javascript
class OrangeSDKManager {
  constructor()
  
  // SDK lifecycle
  initialize()
  gameLoaded()
  gameOver(score)
  gamePaused()
  gameResumed()
  
  // Data management
  saveGameData(data)
  getGameData(defaultData, callback)
  
  // Event listeners
  listenPaused(callback)
  listenResumed(callback)
  listenQuit(callback)
}
```

### 4. Orange ID Authentication (`orangeID.js`)

**Purpose:** Handles user authentication through Orange ID system.

**Key Responsibilities:**
- Manage user login/logout
- Handle authentication callbacks
- Maintain user session state

**Interface:**
```javascript
class OrangeIDManager {
  constructor()
  
  // Authentication
  initializeAuth()
  handleAuthCallback()
  signOut()
  
  // User management
  getCurrentUser()
  isAuthenticated()
}
```

### 5. Technology Manager (`technologyManager.js`)

**Purpose:** Manages the technology research system and unlocks.

**Key Responsibilities:**
- Handle technology research progression
- Validate research prerequisites
- Apply technology effects to game state
- Manage research queue and timing

**Interface:**
```javascript
class TechnologyManager {
  constructor(gameEngine)
  
  // Research management
  startResearch(technologyId)
  updateResearchProgress(deltaTime)
  completeResearch(technologyId)
  
  // Technology queries
  getAvailableTechnologies()
  getTechnologyCost(technologyId)
  canResearch(technologyId)
  
  // Effects application
  applyTechnologyEffects(technologyId)
  getTechnologyMultipliers()
}
```

### 6. Event Manager (`eventManager.js`)

**Purpose:** Handles random events, crises, and player choices.

**Key Responsibilities:**
- Generate random events based on probability
- Present event choices to players
- Apply event consequences to game state
- Manage IMF loans and international politics

**Interface:**
```javascript
class EventManager {
  constructor(gameEngine, uiManager)
  
  // Event generation
  checkForEvents(deltaTime)
  triggerEvent(eventType, eventId)
  resolveEvent(eventId, choiceId)
  
  // Event types
  triggerLocalCrisis(crisisType)
  triggerInternationalEvent(eventType)
  triggerIMFLoan()
  
  // Event state
  getActiveEvents()
  updateEventTimers(deltaTime)
}
```

### 7. Audio Manager (`audioManager.js`)

**Purpose:** Manages sound effects and audio feedback.

**Key Responsibilities:**
- Play sound effects for user actions
- Manage audio settings and volume
- Handle audio loading and caching

**Interface:**
```javascript
class AudioManager {
  constructor()
  
  // Audio playback
  playClickSound()
  playPurchaseSound()
  playUpgradeSound()
  playEventSound(eventType)
  playVictorySound()
  
  // Audio management
  setVolume(level)
  toggleMute()
  preloadSounds()
}
```

### 8. Animation Manager (`animationManager.js`)

**Purpose:** Handles visual animations and effects.

**Key Responsibilities:**
- Coordinate UI animations
- Manage particle effects
- Handle transition animations

**Interface:**
```javascript
class AnimationManager {
  constructor()
  
  // Animation effects
  animateResourceGain(resourceType, amount, element)
  animatePurchase(generatorType)
  animateUpgrade(generatorType)
  animateEvent(eventType)
  
  // Animation control
  startAnimation(animationId, config)
  stopAnimation(animationId)
  updateAnimations(deltaTime)
}
```

## Data Models

### Complete Game State Model

```javascript
const gameState = {
  // Core resources
  gdp: 0,
  gdpPerSecond: 0,
  totalGDPEarned: 0,
  
  // Secondary resources
  resources: {
    food: 0,
    industry: 0,
    knowledge: 0,
    influence: 0,
    bitcoin: 0
  },
  
  // Resource generation rates
  resourcesPerSecond: {
    food: 0,
    industry: 0,
    knowledge: 0,
    influence: 0,
    bitcoin: 0
  },
  
  // Generators for all resource types
  generators: {
    farm: {
      count: 0,
      baseProduction: 1,
      baseCost: 10,
      costMultiplier: 1.15,
      resourceType: 'food',
      upgrades: {
        level: 0,
        productionMultiplier: 1,
        costReduction: 1
      }
    },
    factory: {
      count: 0,
      baseProduction: 2,
      baseCost: 100,
      costMultiplier: 1.2,
      resourceType: 'industry',
      upgrades: { level: 0, productionMultiplier: 1, costReduction: 1 }
    },
    school: {
      count: 0,
      baseProduction: 0.5,
      baseCost: 500,
      costMultiplier: 1.25,
      resourceType: 'knowledge',
      upgrades: { level: 0, productionMultiplier: 1, costReduction: 1 }
    },
    embassy: {
      count: 0,
      baseProduction: 0.1,
      baseCost: 2000,
      costMultiplier: 1.3,
      resourceType: 'influence',
      upgrades: { level: 0, productionMultiplier: 1, costReduction: 1 }
    },
    bitcoinMiner: {
      count: 0,
      baseProduction: 0.01,
      baseCost: 10000,
      costMultiplier: 1.4,
      resourceType: 'bitcoin',
      upgrades: { level: 0, productionMultiplier: 1, costReduction: 1 }
    }
  },
  
  // Technology tree
  technologies: {
    researched: [],
    available: ['basicAgriculture'],
    inProgress: null,
    researchProgress: 0
  },
  
  // Event system
  events: {
    activeEvents: [],
    eventHistory: [],
    lastEventTime: 0,
    imfLoans: {
      active: [],
      totalDebt: 0,
      interestRate: 0.15
    }
  },
  
  // Victory conditions
  victory: {
    influenceTarget: 100,
    currentInfluence: 0,
    gameWon: false,
    finalScore: 0
  },
  
  // Game metadata
  gameStartTime: Date.now(),
  totalPlayTime: 0,
  totalClicks: 0,
  gamePhase: 1,
  
  // User data
  user: null,
  
  // Version for save compatibility
  version: "1.0.0"
};
```

### Technology Tree Configuration

```javascript
const technologyTree = {
  basicAgriculture: {
    name: "Basic Agriculture",
    description: "Improves farm efficiency by 25%",
    cost: { knowledge: 10 },
    prerequisites: [],
    effects: {
      generatorMultiplier: { farm: 1.25 }
    },
    unlocks: ['irrigation', 'animalHusbandry']
  },
  
  irrigation: {
    name: "Irrigation Systems",
    description: "Reduces farm costs and increases production",
    cost: { knowledge: 50, industry: 20 },
    prerequisites: ['basicAgriculture'],
    effects: {
      generatorMultiplier: { farm: 1.5 },
      costReduction: { farm: 0.9 }
    },
    unlocks: ['modernAgriculture']
  },
  
  industrialization: {
    name: "Industrialization",
    description: "Unlocks factory generators",
    cost: { knowledge: 25 },
    prerequisites: [],
    effects: {
      unlockGenerator: 'factory'
    },
    unlocks: ['massProduction', 'automation']
  },
  
  education: {
    name: "Public Education",
    description: "Unlocks school generators",
    cost: { food: 100, industry: 50 },
    prerequisites: [],
    effects: {
      unlockGenerator: 'school'
    },
    unlocks: ['higherEducation', 'research']
  },
  
  diplomacy: {
    name: "Diplomatic Relations",
    description: "Unlocks embassy generators",
    cost: { knowledge: 100, industry: 100 },
    prerequisites: ['education'],
    effects: {
      unlockGenerator: 'embassy'
    },
    unlocks: ['internationalTrade', 'globalInfluence']
  },
  
  cryptocurrency: {
    name: "Cryptocurrency Adoption",
    description: "Unlocks Bitcoin mining operations",
    cost: { knowledge: 500, industry: 1000 },
    prerequisites: ['automation', 'research'],
    effects: {
      unlockGenerator: 'bitcoinMiner'
    },
    unlocks: ['aiDevelopment']
  },
  
  aiDevelopment: {
    name: "AI Machine God Project",
    description: "Ultimate technology for universal dominance",
    cost: { knowledge: 10000, industry: 50000, bitcoin: 1000 },
    prerequisites: ['cryptocurrency', 'globalInfluence'],
    effects: {
      victoryCondition: true,
      influenceMultiplier: 10
    },
    unlocks: []
  }
};
```

### Event System Configuration

```javascript
const eventTypes = {
  localCrisis: {
    drought: {
      name: "Severe Drought",
      description: "A devastating drought has struck Pooristan",
      probability: 0.1,
      effects: {
        resourceReduction: { food: 0.5 },
        duration: 30000 // 30 seconds
      },
      resolutionOptions: [
        {
          name: "Emergency Water Import",
          cost: { gdp: 1000, industry: 50 },
          effects: { resourceReduction: { food: 0.8 } }
        },
        {
          name: "Do Nothing",
          cost: {},
          effects: { resourceReduction: { food: 0.3 } }
        }
      ]
    },
    
    civilUnrest: {
      name: "Civil Unrest",
      description: "Citizens are protesting economic conditions",
      probability: 0.08,
      effects: {
        resourceReduction: { gdp: 0.7, influence: 0.5 },
        duration: 45000
      },
      resolutionOptions: [
        {
          name: "Increase Social Programs",
          cost: { gdp: 2000, food: 100 },
          effects: { resourceReduction: { gdp: 0.9, influence: 1.2 } }
        },
        {
          name: "Suppress Protests",
          cost: { industry: 100 },
          effects: { resourceReduction: { gdp: 0.8, influence: 0.3 } }
        }
      ]
    }
  },
  
  internationalPolitics: {
    tradeDeal: {
      name: "International Trade Agreement",
      description: "A foreign nation offers a trade partnership",
      probability: 0.05,
      options: [
        {
          name: "Accept Deal",
          shortTermEffects: { resourceMultiplier: { industry: 1.5 } },
          longTermEffects: { resourceMultiplier: { influence: 0.8 } },
          duration: 120000
        },
        {
          name: "Reject Deal",
          effects: { resourceMultiplier: { influence: 1.1 } }
        }
      ]
    }
  },
  
  imfLoan: {
    emergencyLoan: {
      name: "IMF Emergency Loan Offer",
      description: "The IMF offers financial assistance with conditions",
      triggerCondition: { gdp: { lessThan: 500 } },
      loanAmount: 5000,
      interestRate: 0.15,
      austerityMeasures: {
        resourceMultiplier: {
          food: 0.8,
          industry: 0.7,
          knowledge: 0.6
        }
      },
      repaymentPeriod: 300000 // 5 minutes
    }
  }
};
```

### Complete Generator Configuration

```javascript
const generatorConfig = {
  farm: {
    name: "Farm",
    description: "Produces food and basic GDP",
    baseProduction: 1,
    baseCost: 10,
    costMultiplier: 1.15,
    resourceType: 'food',
    maxUpgradeLevel: 10,
    upgradeConfig: {
      baseCost: 50,
      costMultiplier: 2.0,
      productionIncrease: 0.5
    },
    unlockRequirement: null
  },
  
  factory: {
    name: "Factory",
    description: "Produces industrial goods",
    baseProduction: 2,
    baseCost: 100,
    costMultiplier: 1.2,
    resourceType: 'industry',
    maxUpgradeLevel: 15,
    upgradeConfig: {
      baseCost: 200,
      costMultiplier: 2.2,
      productionIncrease: 0.75
    },
    unlockRequirement: 'industrialization'
  },
  
  school: {
    name: "School",
    description: "Generates knowledge through education",
    baseProduction: 0.5,
    baseCost: 500,
    costMultiplier: 1.25,
    resourceType: 'knowledge',
    maxUpgradeLevel: 12,
    upgradeConfig: {
      baseCost: 1000,
      costMultiplier: 2.5,
      productionIncrease: 1.0
    },
    unlockRequirement: 'education'
  },
  
  embassy: {
    name: "Embassy",
    description: "Builds international influence",
    baseProduction: 0.1,
    baseCost: 2000,
    costMultiplier: 1.3,
    resourceType: 'influence',
    maxUpgradeLevel: 8,
    upgradeConfig: {
      baseCost: 5000,
      costMultiplier: 3.0,
      productionIncrease: 2.0
    },
    unlockRequirement: 'diplomacy'
  },
  
  bitcoinMiner: {
    name: "Bitcoin Miner",
    description: "Mines cryptocurrency for the nation",
    baseProduction: 0.01,
    baseCost: 10000,
    costMultiplier: 1.4,
    resourceType: 'bitcoin',
    maxUpgradeLevel: 5,
    upgradeConfig: {
      baseCost: 25000,
      costMultiplier: 4.0,
      productionIncrease: 5.0
    },
    unlockRequirement: 'cryptocurrency'
  }
};
```

## Error Handling

### Error Categories and Strategies

1. **Orange SDK Errors**
   - Network connectivity issues
   - Invalid save data format
   - SDK initialization failures
   - Strategy: Graceful degradation with local storage fallback

2. **Game State Errors**
   - Corrupted save data
   - Invalid purchase attempts
   - Calculation overflow errors
   - Strategy: State validation and automatic correction

3. **UI Errors**
   - DOM element not found
   - Event handler failures
   - Animation errors
   - Strategy: Error logging and UI recovery

4. **Authentication Errors**
   - Orange ID authentication failures
   - Token expiration
   - Network errors during auth
   - Strategy: Retry mechanisms and fallback to guest mode

### Error Handling Implementation

```javascript
class ErrorHandler {
  static handleSDKError(error, context) {
    console.error(`SDK Error in ${context}:`, error);
    // Fallback to local storage
    return this.fallbackToLocalStorage();
  }
  
  static handleGameStateError(error, state) {
    console.error('Game State Error:', error);
    // Validate and correct state
    return this.validateAndCorrectState(state);
  }
  
  static handleUIError(error, element) {
    console.error('UI Error:', error);
    // Attempt to recover UI element
    return this.recoverUIElement(element);
  }
}
```

## Testing Strategy

### Unit Testing Approach

1. **Game Logic Testing**
   - Resource calculation accuracy
   - Purchase validation logic
   - Upgrade cost calculations
   - State transitions

2. **UI Component Testing**
   - Button state management
   - Display update accuracy
   - Event handler functionality
   - Animation completion

3. **Integration Testing**
   - Orange SDK communication
   - Save/load functionality
   - Authentication flow
   - Cross-browser compatibility

### Testing Implementation

```javascript
// Example test structure
const GameEngineTests = {
  testGDPClick: () => {
    const engine = new GameEngine();
    const initialGDP = engine.gameState.gdp;
    engine.clickGDP();
    assert(engine.gameState.gdp === initialGDP + 1);
  },
  
  testGeneratorPurchase: () => {
    const engine = new GameEngine();
    engine.gameState.gdp = 100;
    const result = engine.purchaseGenerator('farm', 1);
    assert(result === true);
    assert(engine.gameState.generators.farm.count === 1);
  }
};
```

### Performance Testing

1. **Game Loop Performance**
   - Measure tick execution time
   - Monitor memory usage during extended play
   - Test with multiple generators active

2. **UI Responsiveness**
   - Click response time measurement
   - Animation frame rate monitoring
   - DOM update performance

3. **Save/Load Performance**
   - Large save data handling
   - Network request timing
   - Fallback mechanism speed

## Technical Implementation Details

### Game Loop Architecture

The game uses a fixed timestep game loop with interpolation for smooth visual updates:

```javascript
class GameLoop {
  constructor(gameEngine, uiManager) {
    this.gameEngine = gameEngine;
    this.uiManager = uiManager;
    this.lastTime = 0;
    this.accumulator = 0;
    this.fixedTimeStep = 1000 / 60; // 60 FPS
  }
  
  loop(currentTime) {
    const deltaTime = currentTime - this.lastTime;
    this.lastTime = currentTime;
    this.accumulator += deltaTime;
    
    // Fixed timestep updates
    while (this.accumulator >= this.fixedTimeStep) {
      this.gameEngine.tick(this.fixedTimeStep);
      this.accumulator -= this.fixedTimeStep;
    }
    
    // Interpolated rendering
    const alpha = this.accumulator / this.fixedTimeStep;
    this.uiManager.render(alpha);
    
    requestAnimationFrame(this.loop.bind(this));
  }
}
```

### Resource Generation Algorithm

```javascript
calculateProduction(generator) {
  const baseProduction = generator.baseProduction;
  const count = generator.count;
  const upgradeMultiplier = 1 + (generator.upgrades.level * 0.5);
  
  return baseProduction * count * upgradeMultiplier;
}

calculateCost(generator, quantity = 1) {
  const baseCost = generator.baseCost;
  const currentCount = generator.count;
  const multiplier = generator.costMultiplier;
  
  let totalCost = 0;
  for (let i = 0; i < quantity; i++) {
    totalCost += baseCost * Math.pow(multiplier, currentCount + i);
  }
  
  return Math.floor(totalCost);
}
```

### Orange SDK Integration Pattern

```javascript
class OrangeSDKManager {
  async initialize() {
    try {
      // Initialize SDK
      await this.setupSDK();
      
      // Load game data
      const defaultData = this.getDefaultGameState();
      GGSDK.getGameData(defaultData, (data) => {
        this.gameEngine.loadGameState(data);
        this.uiManager.updateAllDisplays();
      });
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Signal game loaded
      GGSDK.gameLoaded();
      
    } catch (error) {
      ErrorHandler.handleSDKError(error, 'initialization');
    }
  }
  
  saveGame() {
    try {
      const gameState = this.gameEngine.getGameState();
      GGSDK.saveGameData(gameState);
    } catch (error) {
      ErrorHandler.handleSDKError(error, 'save');
    }
  }
}
```

This design provides a solid foundation for Phase 1 while maintaining extensibility for future phases. The modular architecture allows for easy addition of new generator types, upgrade systems, and game features without major refactoring.