# Pooristan: A Development Roadmap

## 1. Game Concept

**Title:** Pooristan: From Dust to Dominance

**Elevator Pitch:** An engaging idle clicker game where players take on the role of the leader of Pooristan, a fictional, resource-scarce nation. The goal is to transform Pooristan into a thriving world power by making strategic decisions, managing resources, and navigating a treacherous landscape of local crises, international politics, and predatory financial institutions.

**Core Theme:** Resilience, strategic growth, and satire on international relations and economics.

**Target Audience:** Players who enjoy idle games, strategy games, and dark humor.

**Monetization:** The game will be free-to-play and will include online multiplayer.

## 2. Core Gameplay Mechanics

*   **Resource Generation:**
    *   **Primary Resource: "GDP"** - Gross Domestic Product.
    *   **Secondary Resources:** As the game progresses, players unlock automated resource generators:
        *   **Food:** Farms, fisheries.
        *   **Industry:** Factories, mines.
        *   **Knowledge:** Schools, universities.
        *   **Influence:** Embassies, propaganda towers.
        *   **Bitcoin Development** Mining initiatives, making it legal tender, etc.
        *   **AI Machine God Project** Create enough HPC compute clusters to make an AI that can unlock the secrets of the universe (and completely take the fun out of life).
*   **Upgrade System:**
    *   Players can spend resources to upgrade generators, increasing their output.
    *   A deep tech tree will allow players to unlock new buildings, upgrades, and abilities that increase GDP, reduce risk/impact of disasters and difficulties, and decrease reliance on foreign aid/loans.
*   **Event System:**
    *   **Local Crises:** Random events like droughts, floods, civil unrest, and corruption scandals will occur, causing issues that reduce GDP and require attention and dedication of resources to fix. These will be events that show in a pop up dialog on occasion.
    *   **International Politics:** Players will receive offers and demands from other nations. For example, a powerful nation might offer a trade deal that provides a short-term boost but has long-term negative consequences.
    *   **The International Monetary Fund (IMF):** The IMF will periodically offer predatory loans to "help" after disasters and difficulties with steep interest rates and attached "austerity measures" that will negatively impact resource generation and control over strategic resources. Players can choose to accept or reject these offers, with consequences for each choice. The aim is to make the difficulty steep in the beginning, where it's almost impossible avoid IMF loans early on. These will be shown as notification messages in the corner/sidebar that can be expanded and acted upon, so as to not interrupt the rest of the gameplay.
*   **Winning the Game:** The game is won when Pooristan achieves a set of victory conditions, such as:
    *   Launching a dominant organization that competes with the IMF and achieves 100% "Influence".

## 3. Technical Stack

*   **Platform:** Browser-based HTML5 game.
*   **Languages:** HTML, CSS, JavaScript.
*   **Libraries/Frameworks:** Any that are needed, as long as it meets the Orange SDK requirements in OrangeSDK.md
*   **Authentication:** Orange ID will be used for user authentication and profile management.
*   **Backend:** The game will be client-side only, with game state saved via the Orange SDK.

## 4. Development Phases

### Phase 1: Foundation & Core Gameplay Loop

**Goal:** To build the basic foundation of the game and implement the core gameplay loop.

*   **Tasks:**
    *   Set up the basic HTML, CSS, and JavaScript file structure.
    *   Implement the "GDP" generation mechanic (clicking).
    *   Create the first automated resource generator (e.g., a farm).
    *   Implement the upgrade system for the farm.
    *   Integrate the Orange SDK and implement the `gameLoaded` and `saveGameData`/`getGameData` functions.
    *   Create a simple UI to display resources and the clicker button.

**Outcome:** A playable prototype with the core click-and-upgrade loop.

### Phase 2: Feature Expansion & UI/UX

**Goal:** To add more depth to the game with additional features and a more polished UI.

*   **Tasks:**
    *   Implement the other secondary resources (Industry, Knowledge, Influence).
    *   Design and implement the tech tree.
    *   Develop the event system with at least one event of each type (Local Crisis, International Politics, IMF).
    *   Integrate Orange ID for user login and logout, with a main page with login button. After login the player should be sent to the game.
    *   Design and implement a more comprehensive UI that includes tabs for different sections (e.g., Resources, Upgrades, Events).
    *   Implement the `gameOver` function from the Orange SDK.

**Outcome:** A feature-rich version of the game with a functional UI and user authentication.

### Phase 3: Polishing & Balancing

**Goal:** To refine the gameplay, balance the game's economy, and add polish.

*   **Tasks:**
    *   Balance the costs of upgrades and the rate of resource generation.
    *   Add more events to the event system.
    *   Add sound effects for clicking, upgrades, and events.
    *   Add visual feedback for player actions (e.g., animations).
    *   Implement the `gamePaused`, `gameResumed`, `listenPaused`, `listenResumed`, and `listenQuit` functions from the Orange SDK.
    *   Thoroughly test the game for bugs and gameplay issues.

**Outcome:** A polished and balanced game that is ready for pre-launch testing.

### Phase 4: Pre-launch & Deployment

**Goal:** To prepare the game for deployment.

*   **Tasks:**
    *   Final round of testing and bug fixing.
    *   Optimize the game's assets to ensure the total file size is under 7MB.
    *   Minify and obfuscate the JavaScript code.
    *   Deploy the game to a web server.
    *   Ensure all Orange SDK and Orange ID requirements are met.

**Outcome:** A production-ready game that is deployed and ready for players.
