<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIManager Test</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        body {
            padding: 2rem;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(52, 73, 94, 0.6);
            border-radius: 10px;
        }
        .test-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        .test-btn {
            padding: 0.5rem 1rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(44, 62, 80, 0.6);
            border-radius: 5px;
            min-height: 100px;
        }
        .test-pass {
            color: #2ecc71;
        }
        .test-fail {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <h1>UIManager Test</h1>
    
    <div class="test-section">
        <h2>Test Environment</h2>
        
        <!-- GDP Display Test -->
        <div class="gdp-display">
            <h2>GDP: $<span id="gdp-amount">0</span></h2>
            <p>Per Second: $<span id="gdp-per-second">0</span></p>
        </div>
        
        <button id="gdp-click-btn" class="gdp-click-btn">
            Click for GDP!
        </button>
        
        <!-- Tab Test -->
        <div class="game-tabs">
            <div class="tab-buttons">
                <button class="tab-btn active" data-tab="resources">Resources</button>
                <button class="tab-btn" data-tab="upgrades">Upgrades</button>
                <button class="tab-btn" data-tab="events">Events</button>
                <button class="tab-btn" data-tab="tech">Tech Tree</button>
            </div>
            
            <div class="tab-content">
                <div id="resources-tab" class="tab-panel active">
                    <h3>Resources</h3>
                    <div class="resource-list" id="resource-list">
                        <!-- Resources will be populated by JavaScript -->
                    </div>
                </div>
                
                <div id="upgrades-tab" class="tab-panel">
                    <h3>Generators & Upgrades</h3>
                    <div class="generator-list" id="generator-list">
                        <!-- Generators will be populated by JavaScript -->
                    </div>
                </div>
                
                <div id="events-tab" class="tab-panel">
                    <h3>Events</h3>
                    <div class="event-list" id="event-list">
                        <p>No active events</p>
                    </div>
                </div>
                
                <div id="tech-tab" class="tab-panel">
                    <h3>Technology Tree</h3>
                    <div class="tech-tree" id="tech-tree">
                        <!-- Tech tree will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Game Stats -->
        <div class="game-footer">
            <div class="game-stats">
                <span>Total Clicks: <span id="total-clicks">0</span></span>
                <span>Play Time: <span id="play-time">00:00</span></span>
                <span>Phase: <span id="game-phase">1</span></span>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>UI Manager Tests</h2>
        
        <div class="test-buttons">
            <button id="test-init-btn" class="test-btn">Test Initialization</button>
            <button id="test-click-btn" class="test-btn">Test GDP Click</button>
            <button id="test-purchase-btn" class="test-btn">Test Purchase</button>
            <button id="test-upgrade-btn" class="test-btn">Test Upgrade</button>
            <button id="test-notification-btn" class="test-btn">Test Notification</button>
        </div>
        
        <div id="test-result" class="test-result">
            <p>Click a test button to run tests...</p>
        </div>
    </div>
    
    <!-- User Info for Testing -->
    <div id="user-info" style="display: none;">
        <span id="user-name">Test User</span>
    </div>
    
    <!-- Event Modal -->
    <div id="event-modal" class="modal hidden">
        <div class="modal-content">
            <h3 id="event-title"></h3>
            <p id="event-description"></p>
            <div id="event-choices" class="event-choices">
                <!-- Event choices will be populated by JavaScript -->
            </div>
        </div>
    </div>
    
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen hidden">
        <div class="loading-content">
            <h2>Loading Pooristan...</h2>
            <div class="loading-spinner"></div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="js/utils.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/uiManager.js"></script>
    
    <script>
        // Test UIManager functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Create test instances
            const gameEngine = new GameEngine();
            const uiManager = new UIManager(gameEngine);
            
            // Initialize game state for testing
            gameEngine.gameState.gdp = 100;
            gameEngine.updateGDPPerSecond();
            
            // Test result display
            const testResult = document.getElementById('test-result');
            
            // Test initialization
            document.getElementById('test-init-btn').addEventListener('click', () => {
                try {
                    uiManager.initialize();
                    testResult.innerHTML = '<p class="test-pass">✅ Initialization successful!</p>';
                    testResult.innerHTML += '<p>UI Manager initialized and displays updated.</p>';
                } catch (error) {
                    testResult.innerHTML = `<p class="test-fail">❌ Initialization failed: ${error.message}</p>`;
                }
            });
            
            // Test GDP click
            document.getElementById('test-click-btn').addEventListener('click', () => {
                try {
                    const initialGDP = gameEngine.gameState.gdp;
                    uiManager.handleGDPClick();
                    
                    if (gameEngine.gameState.gdp === initialGDP + 1) {
                        testResult.innerHTML = '<p class="test-pass">✅ GDP Click test passed!</p>';
                        testResult.innerHTML += `<p>GDP increased from ${initialGDP} to ${gameEngine.gameState.gdp}</p>`;
                        testResult.innerHTML += '<p>Click animation and floating number should be visible.</p>';
                    } else {
                        testResult.innerHTML = '<p class="test-fail">❌ GDP Click test failed!</p>';
                        testResult.innerHTML += `<p>GDP did not increase correctly. Expected: ${initialGDP + 1}, Actual: ${gameEngine.gameState.gdp}</p>`;
                    }
                } catch (error) {
                    testResult.innerHTML = `<p class="test-fail">❌ GDP Click test failed: ${error.message}</p>`;
                }
            });
            
            // Test purchase
            document.getElementById('test-purchase-btn').addEventListener('click', () => {
                try {
                    const initialCount = gameEngine.gameState.generators.farm.count;
                    uiManager.handleGeneratorPurchase('farm', 1);
                    
                    if (gameEngine.gameState.generators.farm.count === initialCount + 1) {
                        testResult.innerHTML = '<p class="test-pass">✅ Purchase test passed!</p>';
                        testResult.innerHTML += `<p>Farm count increased from ${initialCount} to ${gameEngine.gameState.generators.farm.count}</p>`;
                        testResult.innerHTML += '<p>Purchase animation should be visible.</p>';
                    } else {
                        testResult.innerHTML = '<p class="test-fail">❌ Purchase test failed!</p>';
                        testResult.innerHTML += `<p>Farm count did not increase correctly. Expected: ${initialCount + 1}, Actual: ${gameEngine.gameState.generators.farm.count}</p>`;
                    }
                } catch (error) {
                    testResult.innerHTML = `<p class="test-fail">❌ Purchase test failed: ${error.message}</p>`;
                }
            });
            
            // Test upgrade
            document.getElementById('test-upgrade-btn').addEventListener('click', () => {
                try {
                    // Ensure we have at least one farm
                    if (gameEngine.gameState.generators.farm.count === 0) {
                        gameEngine.gameState.generators.farm.count = 1;
                    }
                    
                    const initialLevel = gameEngine.gameState.generators.farm.upgrades.level;
                    uiManager.handleGeneratorUpgrade('farm');
                    
                    if (gameEngine.gameState.generators.farm.upgrades.level === initialLevel + 1) {
                        testResult.innerHTML = '<p class="test-pass">✅ Upgrade test passed!</p>';
                        testResult.innerHTML += `<p>Farm upgrade level increased from ${initialLevel} to ${gameEngine.gameState.generators.farm.upgrades.level}</p>`;
                        testResult.innerHTML += '<p>Upgrade animation should be visible.</p>';
                    } else {
                        testResult.innerHTML = '<p class="test-fail">❌ Upgrade test failed!</p>';
                        testResult.innerHTML += `<p>Farm upgrade level did not increase correctly. Expected: ${initialLevel + 1}, Actual: ${gameEngine.gameState.generators.farm.upgrades.level}</p>`;
                    }
                } catch (error) {
                    testResult.innerHTML = `<p class="test-fail">❌ Upgrade test failed: ${error.message}</p>`;
                }
            });
            
            // Test notification
            document.getElementById('test-notification-btn').addEventListener('click', () => {
                try {
                    uiManager.showNotification('This is a test notification', 'info');
                    setTimeout(() => {
                        uiManager.showNotification('This is a success notification', 'success');
                    }, 1000);
                    setTimeout(() => {
                        uiManager.showNotification('This is a warning notification', 'warning');
                    }, 2000);
                    setTimeout(() => {
                        uiManager.showNotification('This is an error notification', 'error');
                    }, 3000);
                    
                    testResult.innerHTML = '<p class="test-pass">✅ Notification test started!</p>';
                    testResult.innerHTML += '<p>Four notifications should appear in sequence.</p>';
                } catch (error) {
                    testResult.innerHTML = `<p class="test-fail">❌ Notification test failed: ${error.message}</p>`;
                }
            });
        });
    </script>
</body>
</html>