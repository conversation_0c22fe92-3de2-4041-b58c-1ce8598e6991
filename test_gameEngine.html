<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GameEngine Test</title>
</head>
<body>
    <h1>GameEngine Test</h1>
    <div id="test-results"></div>

    <script src="js/gameEngine.js"></script>
    <script>
        // Test GameEngine functionality
        function runTests() {
            const results = [];
            const engine = new GameEngine();
            
            // Test 1: Initial state
            results.push({
                test: "Initial GDP should be 0",
                passed: engine.gameState.gdp === 0
            });
            
            // Test 2: GDP clicking (Requirement 1.1, 1.2)
            const initialGDP = engine.gameState.gdp;
            engine.clickGDP();
            results.push({
                test: "GDP click should increment by 1",
                passed: engine.gameState.gdp === initialGDP + 1
            });
            
            // Test 3: Multiple clicks
            engine.clickGDP();
            engine.clickGDP();
            results.push({
                test: "Multiple clicks should work",
                passed: engine.gameState.gdp === 3 && engine.gameState.totalClicks === 3
            });
            
            // Test 4: Generator purchase validation (Requirement 2.1)
            engine.gameState.gdp = 100;
            const purchaseResult = engine.purchaseGenerator('farm', 1);
            results.push({
                test: "Should be able to purchase farm with sufficient GDP",
                passed: purchaseResult === true && engine.gameState.generators.farm.count === 1
            });
            
            // Test 5: Insufficient funds (Requirement 2.5)
            engine.gameState.gdp = 0;
            const failedPurchase = engine.purchaseGenerator('farm', 1);
            results.push({
                test: "Should not be able to purchase with insufficient GDP",
                passed: failedPurchase === false
            });
            
            // Test 6: Cost calculation (Requirement 2.1)
            engine.gameState.generators.farm.count = 0;
            const cost1 = engine.calculateGeneratorCost('farm', 1);
            results.push({
                test: "First farm should cost 10 GDP",
                passed: cost1 === 10
            });
            
            // Test 7: Exponential cost scaling (Requirement 2.1)
            engine.gameState.generators.farm.count = 1;
            const cost2 = engine.calculateGeneratorCost('farm', 1);
            const expectedCost = Math.floor(10 * Math.pow(1.15, 1));
            results.push({
                test: "Second farm should cost more (exponential scaling)",
                passed: cost2 === expectedCost && cost2 > 10
            });
            
            // Test 8: GDP per second calculation (Requirement 2.2)
            engine.gameState.generators.farm.count = 2;
            engine.updateGDPPerSecond();
            results.push({
                test: "GDP per second should be calculated correctly",
                passed: engine.gameState.gdpPerSecond === 2 // 2 farms * 1 production each
            });
            
            // Test 9: Generator upgrade (Requirement 2.3, 2.4)
            engine.gameState.gdp = 1000;
            const upgradeResult = engine.upgradeGenerator('farm');
            results.push({
                test: "Should be able to upgrade generator",
                passed: upgradeResult === true && engine.gameState.generators.farm.upgrades.level === 1
            });
            
            // Test 10: Upgrade effect on production (Requirement 2.4)
            engine.updateGDPPerSecond();
            results.push({
                test: "Upgrades should increase production",
                passed: engine.gameState.gdpPerSecond === 3 // 2 farms * 1 production * 1.5 multiplier
            });
            
            // Test 11: State validation
            engine.gameState.gdp = -100; // Invalid state
            engine.validateGameState();
            results.push({
                test: "State validation should correct negative GDP",
                passed: engine.gameState.gdp === 0
            });
            
            // Test 12: Game loop start/stop
            engine.startGameLoop();
            const isRunning1 = engine.isRunning;
            engine.stopGameLoop();
            const isRunning2 = engine.isRunning;
            results.push({
                test: "Game loop should start and stop correctly",
                passed: isRunning1 === true && isRunning2 === false
            });
            
            // Test 13: Save/Load state (Requirement 3.3, 3.4)
            const originalState = engine.getGameState();
            engine.gameState.gdp = 999;
            engine.loadGameState(originalState);
            results.push({
                test: "Should be able to save and load game state",
                passed: engine.gameState.gdp === originalState.gdp
            });
            
            // Test 14: Purchase multiple generators (Requirement 2.1)
            engine.gameState.gdp = 100;
            const multiPurchaseResult = engine.purchaseMultipleGenerators('farm', 3);
            results.push({
                test: "Should be able to purchase multiple generators at once",
                passed: multiPurchaseResult === true && engine.gameState.generators.farm.count > 0
            });
            
            // Test 15: Calculate maximum affordable generators (Requirement 2.1, 2.5)
            engine.gameState.gdp = 100;
            engine.gameState.generators.farm.count = 0;
            const maxAffordable = engine.getMaxAffordableGenerators('farm');
            results.push({
                test: "Should calculate maximum affordable generators correctly",
                passed: maxAffordable > 0 && typeof maxAffordable === 'number'
            });
            
            // Test 16: Automatic resource generation (Requirement 2.2)
            engine.gameState.gdp = 0;
            engine.gameState.generators.farm.count = 1;
            engine.gameState.generators.farm.upgrades.level = 0;
            engine.gameState.generators.farm.upgrades.productionMultiplier = 1;
            engine.updateGDPPerSecond();
            
            // Simulate a tick of 1 second
            engine.tick(1000);
            results.push({
                test: "Generators should automatically produce GDP over time",
                passed: engine.gameState.gdp === 1 // 1 farm * 1 production * 1 second
            });
            
            return results;
        }
        
        // Run tests and display results
        const testResults = runTests();
        const resultsDiv = document.getElementById('test-results');
        
        let passedCount = 0;
        let html = '<h2>Test Results:</h2><ul>';
        
        testResults.forEach((result, index) => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            html += `<li><strong>Test ${index + 1}:</strong> ${result.test} - ${status}</li>`;
            if (result.passed) passedCount++;
        });
        
        html += '</ul>';
        html += `<h3>Summary: ${passedCount}/${testResults.length} tests passed</h3>`;
        
        if (passedCount === testResults.length) {
            html += '<p style="color: green; font-weight: bold;">All tests passed! GameEngine implementation is working correctly.</p>';
        } else {
            html += '<p style="color: red; font-weight: bold;">Some tests failed. Please check the implementation.</p>';
        }
        
        resultsDiv.innerHTML = html;
    </script>
</body>
</html>